const behaviors = [
  ['disable', { 'pointer-events': 'none', opacity: 0.5 }],
  ['clickable', { cursor: 'pointer' }],
  [
    'button',
    {
      border: '1px solid #ddd',
      'border-radius': '6px',
      cursor: 'pointer',
      'padding-left': '1rem',
      'padding-right': '1rem',
      'padding-top': '0.5rem',
      'padding-bottom': '0.5rem',
    },
  ],
  ['input', { height: '30px', 'padding-left': '0.5rem', 'padding-right': '0.5rem' }],
  ['select', { height: '30px', 'min-width': '60px' }],
]

/*https://material.io/resources/color*/
const colors = [
  [/^c:(.*?)$/, ([, c]) => ({ color: c })],
  [/^bg:(.*?)$/, ([, bg]) => ({ 'background-color': bg })],

  ['c-primary', { color: '#3ABFF8' }],
  ['c-primary-10', { color: '#E3F0FF' }],
  ['c-primary-20', { color: '#D2C8F9' }],
  ['c-primary-30', { color: '#AF9EF4' }],
  ['c-primary-40', { color: '#8D74F0' }],
  ['c-primary-50', { color: '#3ABFF8' }],
  ['c-primary-60', { color: '#C4EBFE' }],
  ['c-primary-70', { color: '#4D35B0' }],
  ['c-primary-80', { color: '#C4EBFE' }],
  ['c-primary-90', { color: '#301F75' }],
  ['c-primary-100', { color: '#0150A0' }],
  ['c-primary-110', { color: '#014080' }],
  ['c-primary-120', { color: '#013060' }],
  ['c-danger', { color: '#FF4A4A' }],
  ['c-green-f2', { color: '#F2FFF5' }],
  ['c-green-43', { color: '#43A856' }],
  ['c-black-40', { color: '#E1E1E1' }],
  ['c-black-50', { color: '#C2C2C2' }],
  ['c-black-60', { color: '#8A8A8A' }],
  ['c-black-80', { color: '#484848' }],
  ['c-black-90', { color: '#313131' }],
  ['c-gray-75', { color: '#343A40' }],
  ['c-white', { color: '#ffffff' }],
  ['c-border', { color: '#EFEFEF' }],
  ['c-r-error', { color: '#FF4A4A' }],
  ['c-gr-text', { color: '#2AC86B' }],
  ['c-ye-dark', { color: '#F6A609' }],
  ['c-red-11', { color: '#FC4E4F' }],
  ['c-secondary-80', { color: '#FFCA60' }],
  ['c-semantic', { color: '#2187FF' }],
  ['c-blue', { color: '#017FFF' }],

  ['c-r-0', { color: '#ffebee' }],
  ['c-r-1', { color: '#ffcdd2' }],
  ['c-r-2', { color: '#ef9a9a' }],
  ['c-r-3', { color: '#e57373' }],
  ['c-r-4', { color: '#ef5350' }],
  ['c-r-5', { color: '#f44336' }],
  ['c-r-6', { color: '#e53935' }],
  ['c-r-7', { color: '#d32f2f' }],
  ['c-r-8', { color: '#c62828' }],
  ['c-r-9', { color: '#b71c1c' }],

  ['c-p-0', { color: '#fce4ec' }],
  ['c-p-1', { color: '#f8bbd0' }],
  ['c-p-2', { color: '#f48fb1' }],
  ['c-p-3', { color: '#f06292' }],
  ['c-p-4', { color: '#ec407a' }],
  ['c-p-5', { color: '#e91e63' }],
  ['c-p-6', { color: '#d81b60' }],
  ['c-p-7', { color: '#c2185b' }],
  ['c-p-8', { color: '#ad1457' }],
  ['c-p-9', { color: '#880e4f' }],

  ['c-pp-0', { color: '#f3e5f5' }],
  ['c-pp-1', { color: '#e1bee7' }],
  ['c-pp-2', { color: '#ce93d8' }],
  ['c-pp-3', { color: '#ba68c8' }],
  ['c-pp-4', { color: '#ab47bc' }],
  ['c-pp-5', { color: '#9c27b0' }],
  ['c-pp-6', { color: '#8e24aa' }],
  ['c-pp-7', { color: '#7b1fa2' }],
  ['c-pp-8', { color: '#6a1b9a' }],
  ['c-pp-9', { color: '#4a148c' }],

  ['c-dpp-0', { color: '#ede7f6' }],
  ['c-dpp-1', { color: '#d1c4e9' }],
  ['c-dpp-2', { color: '#b39ddb' }],
  ['c-dpp-3', { color: '#9575cd' }],
  ['c-dpp-4', { color: '#7e57c2' }],
  ['c-dpp-5', { color: '#673ab7' }],
  ['c-dpp-6', { color: '#5e35b1' }],
  ['c-dpp-7', { color: '#512da8' }],
  ['c-dpp-8', { color: '#4527a0' }],
  ['c-dpp-9', { color: '#311b92' }],

  ['c-i-0', { color: '#e8eaf6' }],
  ['c-i-1', { color: '#c5cae9' }],
  ['c-i-2', { color: '#9fa8da' }],
  ['c-i-3', { color: '#7986cb' }],
  ['c-i-4', { color: '#5c6bc0' }],
  ['c-i-5', { color: '#3f51b5' }],
  ['c-i-6', { color: '#3949ab' }],
  ['c-i-7', { color: '#303f9f' }],
  ['c-i-8', { color: '#283593' }],
  ['c-i-9', { color: '#1a237e' }],

  ['c-b-0', { color: '#e3f2fd' }],
  ['c-b-1', { color: '#bbdefb' }],
  ['c-b-2', { color: '#90caf9' }],
  ['c-b-3', { color: '#64b5f6' }],
  ['c-b-4', { color: '#42a5f5' }],
  ['c-b-5', { color: '#2196f3' }],
  ['c-b-6', { color: '#1e88e5' }],
  ['c-b-7', { color: '#1976d2' }],
  ['c-b-8', { color: '#1565c0' }],
  ['c-b-9', { color: '#0d47a1' }],

  ['c-lb-0', { color: '#e1f5fe' }],
  ['c-lb-1', { color: '#b3e5fc' }],
  ['c-lb-2', { color: '#81d4fa' }],
  ['c-lb-3', { color: '#4fc3f7' }],
  ['c-lb-4', { color: '#29b6f6' }],
  ['c-lb-5', { color: '#03a9f4' }],
  ['c-lb-6', { color: '#039be5' }],
  ['c-lb-7', { color: '#0288d1' }],
  ['c-lb-8', { color: '#0277bd' }],
  ['c-lb-9', { color: '#01579b' }],

  ['c-gre-0', { color: '#e8f5e9' }],
  ['c-gre-1', { color: '#c8e6c9' }],
  ['c-gre-2', { color: '#a5d6a7' }],
  ['c-gre-3', { color: '#81c784' }],
  ['c-gre-4', { color: '#66bb6a' }],
  ['c-gre-5', { color: '#66bb6a' }],
  ['c-gre-6', { color: '#43a047' }],
  ['c-gre-7', { color: '#388e3c' }],
  ['c-gre-8', { color: '#2e7d32' }],
  ['c-gre-9', { color: '#1b5e20' }],
  ['c-gre-99', { color: '#66B975' }],

  ['c-gray-0', { color: '#fafafa' }],
  ['c-gray-1', { color: '#f5f5f5' }],
  ['c-gray-2', { color: '#eeeeee' }],
  ['c-gray-3', { color: '#e0e0e0' }],
  ['c-gray-4', { color: '#bdbdbd' }],
  ['c-gray-5', { color: '#9e9e9e' }],
  ['c-gray-6', { color: '#757575' }],
  ['c-gray-7', { color: '#616161' }],
  ['c-gray-8', { color: '#424242' }],
  ['c-gray-9', { color: '#212121' }],
  ['c-gray-25', { color: '#ADB5BD' }],
  ['c-gray-35', { color: '#A3A3A6' }],
  ['c-gray-40', { color: '#7E7E80' }],
  ['c-gray-50', { color: '#373738' }],

  ['bg-danger', { 'background-color': '#FFF0F0' }],
  ['bg-black-10', { 'background-color': '#F4F4F4' }],
  ['bg-black-20', { 'background-color': '#EFEFEF' }],
  ['bg-black-30', { 'background-color': '#E9E9E9' }],
  ['bg-black-40', { 'background-color': '#E1E1E1' }],
  ['bg-black-50', { 'background-color': '#C2C2C2' }],
  ['bg-primary', { 'background-color': '#3ABFF8' }],
  ['bg-primary-0', { 'background-color': '#E0E5F2' }],
  ['bg-primary-10', { 'background-color': '#E3F0FF' }],
  ['bg-primary-20', { 'background-color': '#E3F0FF' }],
  ['bg-primary-80', { 'background-color': '#C4EBFE' }],
  ['bg-green-f2', { 'background-color': '#F2FFF5' }],
  ['bg-green-43', { 'background-color': '#43A856' }],
  ['bg-yellow-light', { 'background-color': '#FFF2D8' }],
  ['bg-green-light', { 'background-color': '#E7FEE9' }],
  ['bg-secondary-5', { 'background-color': '#FFFAF0' }],
  ['bg-green-tint', { 'background-color': '#48D58C' }],
  ['bg-tint-shamrock-10', { 'background-color': '#E8FAF1' }],
  ['bg-yellow', { 'background-color': '#FFCA60' }],
  ['bg-error', { 'background-color': '#FF4A4A' }],
  ['bg-semantic', { 'background-color': '#2187FF' }],
  ['bg-semantic-10', { 'background-color': '#E3F0FF' }],
  ['bg-blue-100', { 'background-color': '#E3F0FF' }],

  ['bg-r-0', { 'background-color': '#ffebee' }],
  ['bg-r-1', { 'background-color': '#ffcdd2' }],
  ['bg-r-2', { 'background-color': '#ef9a9a' }],
  ['bg-r-3', { 'background-color': '#e57373' }],
  ['bg-r-4', { 'background-color': '#ef5350' }],
  ['bg-r-5', { 'background-color': '#f44336' }],
  ['bg-r-6', { 'background-color': '#e53935' }],
  ['bg-r-7', { 'background-color': '#d32f2f' }],
  ['bg-r-8', { 'background-color': '#c62828' }],
  ['bg-r-9', { 'background-color': '#b71c1c' }],

  ['bg-p-0', { 'background-color': '#fce4ec' }],
  ['bg-p-1', { 'background-color': '#f8bbd0' }],
  ['bg-p-2', { 'background-color': '#f48fb1' }],
  ['bg-p-3', { 'background-color': '#f06292' }],
  ['bg-p-4', { 'background-color': '#ec407a' }],
  ['bg-p-5', { 'background-color': '#e91e63' }],
  ['bg-p-6', { 'background-color': '#d81b60' }],
  ['bg-p-7', { 'background-color': '#c2185b' }],
  ['bg-p-8', { 'background-color': '#ad1457' }],
  ['bg-p-9', { 'background-color': '#880e4f' }],

  ['bg-pp-0', { 'background-color': '#f3e5f5' }],
  ['bg-pp-1', { 'background-color': '#e1bee7' }],
  ['bg-pp-2', { 'background-color': '#ce93d8' }],
  ['bg-pp-3', { 'background-color': '#ba68c8' }],
  ['bg-pp-4', { 'background-color': '#ab47bc' }],
  ['bg-pp-5', { 'background-color': '#9c27b0' }],
  ['bg-pp-6', { 'background-color': '#8e24aa' }],
  ['bg-pp-7', { 'background-color': '#7b1fa2' }],
  ['bg-pp-8', { 'background-color': '#6a1b9a' }],
  ['bg-pp-9', { 'background-color': '#4a148c' }],

  ['bg-dpp-0', { 'background-color': '#ede7f6' }],
  ['bg-dpp-1', { 'background-color': '#d1c4e9' }],
  ['bg-dpp-2', { 'background-color': '#b39ddb' }],
  ['bg-dpp-3', { 'background-color': '#9575cd' }],
  ['bg-dpp-4', { 'background-color': '#7e57c2' }],
  ['bg-dpp-5', { 'background-color': '#673ab7' }],
  ['bg-dpp-6', { 'background-color': '#5e35b1' }],
  ['bg-dpp-7', { 'background-color': '#512da8' }],
  ['bg-dpp-8', { 'background-color': '#4527a0' }],
  ['bg-dpp-9', { 'background-color': '#311b92' }],

  ['bg-i-0', { 'background-color': '#e8eaf6' }],
  ['bg-i-1', { 'background-color': '#c5cae9' }],
  ['bg-i-2', { 'background-color': '#9fa8da' }],
  ['bg-i-3', { 'background-color': '#7986cb' }],
  ['bg-i-4', { 'background-color': '#5c6bc0' }],
  ['bg-i-5', { 'background-color': '#3f51b5' }],
  ['bg-i-6', { 'background-color': '#3949ab' }],
  ['bg-i-7', { 'background-color': '#303f9f' }],
  ['bg-i-8', { 'background-color': '#283593' }],
  ['bg-i-9', { 'background-color': '#1a237e' }],

  ['bg-b-0', { 'background-color': '#e3f2fd' }],
  ['bg-b-1', { 'background-color': '#bbdefb' }],
  ['bg-b-2', { 'background-color': '#90caf9' }],
  ['bg-b-3', { 'background-color': '#64b5f6' }],
  ['bg-b-4', { 'background-color': '#42a5f5' }],
  ['bg-b-5', { 'background-color': '#2196f3' }],
  ['bg-b-6', { 'background-color': '#1e88e5' }],
  ['bg-b-7', { 'background-color': '#1976d2' }],
  ['bg-b-8', { 'background-color': '#1565c0' }],
  ['bg-b-9', { 'background-color': '#0d47a1' }],

  ['bg-lb-0', { 'background-color': '#e1f5fe' }],
  ['bg-lb-1', { 'background-color': '#b3e5fc' }],
  ['bg-lb-2', { 'background-color': '#81d4fa' }],
  ['bg-lb-3', { 'background-color': '#4fc3f7' }],
  ['bg-lb-4', { 'background-color': '#29b6f6' }],
  ['bg-lb-5', { 'background-color': '#03a9f4' }],
  ['bg-lb-6', { 'background-color': '#039be5' }],
  ['bg-lb-7', { 'background-color': '#0288d1' }],
  ['bg-lb-8', { 'background-color': '#0277bd' }],
  ['bg-lb-9', { 'background-color': '#01579b' }],

  ['bg-gre-0', { 'background-color': '#e8f5e9' }],
  ['bg-gre-1', { 'background-color': '#c8e6c9' }],
  ['bg-gre-2', { 'background-color': '#a5d6a7' }],
  ['bg-gre-3', { 'background-color': '#81c784' }],
  ['bg-gre-4', { 'background-color': '#66bb6a' }],
  ['bg-gre-5', { 'background-color': '#66bb6a' }],
  ['bg-gre-6', { 'background-color': '#43a047' }],
  ['bg-gre-7', { 'background-color': '#388e3c' }],
  ['bg-gre-8', { 'background-color': '#2e7d32' }],
  ['bg-gre-9', { 'background-color': '#1b5e20' }],

  ['bg-gray-0', { 'background-color': '#fafafa' }],
  ['bg-gray-1', { 'background-color': '#f5f5f5' }],
  ['bg-gray-2', { 'background-color': '#eeeeee' }],
  ['bg-gray-3', { 'background-color': '#e0e0e0' }],
  ['bg-gray-4', { 'background-color': '#bdbdbd' }],
  ['bg-gray-5', { 'background-color': '#9e9e9e' }],
  ['bg-gray-6', { 'background-color': '#757575' }],
  ['bg-gray-7', { 'background-color': '#616161' }],
  ['bg-gray-8', { 'background-color': '#424242' }],
  ['bg-gray-9', { 'background-color': '#212121' }],
  ['bg-gray-10', { 'background-color': '#F6F6F7' }],
  ['bg-gray-20', { 'background-color': '#EEEEF0' }],

  ['bg-gray-0-5', { 'background-color': '#fafafa8f' }],
  ['bg-gray-1-5', { 'background-color': '#f5f5f58f' }],
  ['bg-gray-2-5', { 'background-color': '#eeeeee8f' }],
  ['bg-gray-3-5', { 'background-color': '#e0e0e08f' }],
  ['bg-gray-4-5', { 'background-color': '#bdbdbd8f' }],
  ['bg-gray-5-5', { 'background-color': '#9e9e9e8f' }],
  ['bg-gray-6-5', { 'background-color': '#7575758f' }],
  ['bg-gray-7-5', { 'background-color': '#6161618f' }],
  ['bg-gray-8-5', { 'background-color': '#4242428f' }],
  ['bg-gray-9-5', { 'background-color': '#2121218f' }],

  ['bg-black', { 'background-color': '#000' }],
  ['bg-white', { 'background-color': '#fff' }],

  ['image-white', { filter: 'brightness(0) invert(1)' }],
]

const layouts = [
  /* sizing */
  [/^h-(\d+)px/, ([, d]) => ({ height: `${d}px` })],
  [/^w-(\d+)px/, ([, d]) => ({ width: `${d}px` })],
  [/^w-(\d+)%/, ([, d]) => ({ width: `${d}%` })],

  ['icon', { width: '24px', height: '24px' }],
  [/^icon-(\d+)px/, ([, d]) => ({ height: `${d}px`, width: `${d}px` })],

  ['h-50', { height: '100%' }],
  ['h-100', { height: '100%' }],
  ['h-100vh', { height: '100vh' }],
  ['w-50', { width: '50%' }],
  ['w-100', { width: '100%' }],
  ['w-100vw', { width: '100vw' }],
  ['w-fit', { width: 'fit-content' }],
  ['w-max', { width: 'max-content' }],

  /* overflow */
  ['ovf-h', { overflow: 'hidden' }],
  ['ovf-a', { overflow: 'auto' }],
  ['ovf-x-s', { 'overflow-x': 'scroll' }],
  ['ovf-x-h', { 'overflow-x': 'hidden' }],
  ['ovf-x-a', { 'overflow-x': 'auto' }],
  ['ovf-y-s', { 'overflow-y': 'scroll' }],
  ['ovf-y-h', { 'overflow-y': 'hidden' }],
  ['ovf-y-a', { 'overflow-y': 'auto' }],
  [
    'hide-scroll-bar',
    {
      '-ms-overflow-style': 'none',
      /* IE, Edge */ 'scrollbar-width': 'none' /* Firefox */,
    },
  ],
  ['hide-scroll-bar::-webkit-scrollbar', { display: 'none' }] /* Chrome, Safari and Opera */,

  /* flex */
  ['fr', { display: 'flex', 'flex-direction': 'row' }],
  ['fc', { display: 'flex', 'flex-direction': 'column' }],
  ['fw-w', { 'flex-wrap': 'wrap' }],
  [/^fg-(\d+)$/, ([, d]) => ({ gap: `${d / 4}rem` })],
  [/^f(\d+)$/, ([, d]) => ({ flex: d })],
  ['ai-bl', { 'align-items': 'baseline' }],
  ['ai-c', { 'align-items': 'center' }],
  ['ai-e', { 'align-items': 'flex-end' }],
  ['ai-s', { 'align-items': 'flex-start' }],
  ['jc-c', { 'justify-content': 'center' }],
  ['jc-sa', { 'justify-content': 'space-around' }],
  ['jc-sb', { 'justify-content': 'space-between' }],
  ['jc-se', { 'justify-content': 'space-evenly' }],
  ['jc-fe', { 'justify-content': 'flex-end' }],
  ['jc-fs', { 'justify-content': 'flex-start' }],
  ['jc-n', { 'justify-content': 'normal' }],
  ['jc-r', { 'justify-content': 'revert' }],

  /* grid */
  ['grid', { display: 'grid' }],
  [/^gtc-(\d+)-(\d+)$/, ([, d1, d2]) => ({ 'grid-template-columns': `${d1} ${d2}` })],
  [/^gtc-(\d+)-(\d+)-(\d+)$/, ([, d1, d2, d3]) => ({ 'grid-template-columns': `${d1} ${d2} ${d3}` })],
  [
    /^gtc-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4]) => ({
      'grid-template-columns': `${d1} ${d2} ${d3} ${d4}`,
    }),
  ],
  [
    /^gtc-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4, d5]) => ({
      'grid-template-columns': `${d1} ${d2} ${d3} ${d4} ${d5}`,
    }),
  ],
  [
    /^gtc-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4, d5, d6]) => ({
      'grid-template-columns': `${d1} ${d2} ${d3} ${d4} ${d5} ${d6}`,
    }),
  ],
  [/^gtr-(\d+)-(\d+)$/, ([, d1, d2]) => ({ 'grid-template-rows': `${d1} ${d2}` })],
  [/^gtr-(\d+)-(\d+)-(\d+)$/, ([, d1, d2, d3]) => ({ 'grid-template-rows': `${d1} ${d2} ${d3}` })],
  [
    /^gtr-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4]) => ({
      'grid-template-rows': `${d1} ${d2} ${d3} ${d4}`,
    }),
  ],
  [
    /^gtr-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4, d5]) => ({
      'grid-template-rows': `${d1} ${d2} ${d3} ${d4} ${d5}`,
    }),
  ],
  [
    /^gtr-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4, d5, d6]) => ({
      'grid-template-rows': `${d1} ${d2} ${d3} ${d4} ${d5} ${d6}`,
    }),
  ],
]

const borderColor = [
  ['border-black-10', { 'border-color': '#F4F4F4' }],
  ['border-black-20', { 'border-color': '#EFEFEF' }],
  ['border-black-30', { 'border-color': '#E9E9E9' }],
  ['border-black-40', { 'border-color': '#E1E1E1' }],
  ['border-black-50', { 'border-color': '#C2C2C2' }],
  ['border-t-black-50', { 'border-top-color': '#C2C2C2' }],
  ['border-gray-10', { 'border-color': '#F6F6F7' }],
  ['border-gray-20', { 'border-color': '#EEEEF0' }],
  ['border-primary', { 'border-color': '#3ABFF8' }],
  ['border-primary-0', { 'border-color': '#F3EEFF' }],
  ['border-secondary-main', { 'border-color': '#1E394F' }],
  ['border-b-black-20', { 'border-bottom-color': '#EFEFEF' }],
  ['border-b-black-30', { 'border-bottom-color': '#E1E1E1' }],
  ['border-green-f2', { 'border-color': '#F2FFF5' }],
  ['border-green-43', { 'border-color': '#43A856' }],
  ['border-semantic', { 'border-color': '#2187FF' }],
]

const borderRadius = [
  [/^br-(\d+)px$/, ([, d]) => ({ 'border-radius': `${d}px` })],
  [/^br-(\d+)$/, ([, d]) => ({ 'border-radius': `${d / 4}rem` })],
  [/^br-(\d+)pt$/, ([, pt]) => ({ 'border-radius': `${pt}%` })],
  // ['rounded', { 'border-radius': '8px' }],
]

const position = [
  ['abs', { position: 'absolute' }],
  ['fix', { position: 'fixed' }],
  ['rel', { position: 'relative' }],
  ['sta', { position: 'static' }],
  ['sti', { position: 'sticky' }],
  [/^top-(\d+)/, ([, d]) => ({ top: `${d}px` })],
  [/^left-(\d+)/, ([, d]) => ({ left: `${d}px` })],
  [/^bottom-(\d+)/, ([, d]) => ({ bottom: `${d}px` })],
  [/^right-(\d+)/, ([, d]) => ({ right: `${d}px` })],
]

const spacing = [
  [/^p-(\d+)$/, ([, d]) => ({ padding: `${d / 4}rem` })],
  [/^pt-(\d+)$/, ([, d]) => ({ 'padding-top': `${d / 4}rem` })],
  [/^pl-(\d+)$/, ([, d]) => ({ 'padding-left': `${d / 4}rem` })],
  [/^pb-(\d+)$/, ([, d]) => ({ 'padding-bottom': `${d / 4}rem` })],
  [/^pr-(\d+)$/, ([, d]) => ({ 'padding-right': `${d / 4}rem` })],
  [
    /^px-(\d+)$/,
    ([, d]) => ({
      'padding-left': `${d / 4}rem`,
      'padding-right': `${d / 4}rem`,
    }),
  ],
  [
    /^py-(\d+)$/,
    ([, d]) => ({
      'padding-top': `${d / 4}rem`,
      'padding-bottom': `${d / 4}rem`,
    }),
  ],

  [/^m-(\d+)$/, ([, d]) => ({ margin: `${d / 4}rem` })],
  [/^mt-(\d+)$/, ([, d]) => ({ 'margin-top': `${d / 4}rem` })],
  [/^ml-(\d+)$/, ([, d]) => ({ 'margin-left': `${d / 4}rem` })],
  [/^mb-(\d+)$/, ([, d]) => ({ 'margin-bottom': `${d / 4}rem` })],
  [/^mr-(\d+)$/, ([, d]) => ({ 'margin-right': `${d / 4}rem` })],
  [/^mx-(\d+)$/, ([, d]) => ({ 'margin-left': `${d / 4}rem`, 'margin-right': `${d / 4}rem` })],
  [/^my-(\d+)$/, ([, d]) => ({ 'margin-top': `${d / 4}rem`, 'margin-bottom': `${d / 4}rem` })],
  ['mx-a', { margin: '0 auto' }],
]

const text = [
  ['t-t--u', { 'text-transform': 'uppercase' }],
  ['t-t--c', { 'text-transform': 'capitalize' }],
  ['t-t--l', { 'text-transform': 'lowercase' }],
  ['ta-l', { 'text-align': 'left' }],
  ['ta-r', { 'text-align': 'right' }],
  ['ta-c', { 'text-align': 'center' }],
  [/^fw-(\d)/, ([_, d]) => ({ 'font-weight': `${d}00` })],
  ['fs-xs', { 'font-size': '0.75rem' }],
  ['fs-s', { 'font-size': '0.875rem' }],
  ['fs-m', { 'font-size': '1rem' }],
  ['fs-l', { 'font-size': '1.15em' }],
  ['text-xs', { 'font-size': '0.75rem', 'line-height': '1rem' }],
  ['text-s', { 'font-size': '0.875rem', 'line-height': '1.25rem' }],
  ['text-m', { 'font-size': '1rem', 'line-height': '1.5rem' }],
  ['text-l', { 'font-size': '1.25rem', 'line-height': '1.875rem' }],

  [/^fs-(\d+)px$/, ([, d]) => ({ 'font-size': `${d}px` })],
  [/^fs-(\d+)rem$/, ([, d]) => ({ 'font-size': `${d}rem` })],
  [/^fs-(\d+)rem$/, ([, d]) => ({ 'font-size': `${d}rem` })],
]

const layering = [[/^z-index-(\d+)$/, ([, d]) => ({ 'z-index': d })]]

export default [
  ...behaviors,
  ...colors,
  ...layouts,
  ...borderColor,
  ...borderRadius,
  ...position,
  ...spacing,
  ...text,
  ...layering,
]
