{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "build:prod": "nuxt build --dotenv .env.prod", "prod": "nuxt dev  --dotenv .env.prod", "generate:prod": "nuxt generate --dotenv .env.prod", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@ckeditor/ckeditor5-vue": "^5.1.0", "@nuxtjs/i18n": "^9.5.4", "@pinia/nuxt": "^0.11.0", "@vee-validate/nuxt": "^4.15.0", "@vueuse/nuxt": "13.2.0", "ckeditor5-custom-build": "file:ckeditor5", "dayjs-nuxt": "2.1.11", "nuxt": "^3.17.3", "nuxt-aos": "^1.2.5", "pinia": "^3.0.2", "qrcode-vue3": "^1.7.3", "read-excel-file": "^5.8.8", "sass-embedded": "^1.88.0", "socket.io-client": "^4.0.1", "vue": "^3.5.13", "vue-json-excel3": "^1.0.30", "vue-router": "^4.5.1", "vue3-markdown": "^1.2.12", "vue3-observe-visibility": "^1.0.3", "vue3-otp-input": "^0.5.40", "vuedraggable": "^4.1.0", "xlsx-js-style": "^1.2.0"}, "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447", "devDependencies": {"@primeuix/themes": "^1.1.1", "@primevue/nuxt-module": "^4.2.5", "@unocss/nuxt": "^66.1.1", "postcss": "^8.5.3", "primevue": "^4.2.5", "unocss": "^66.1.1"}}