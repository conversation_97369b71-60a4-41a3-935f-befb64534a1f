{"common": {"register": "Register", "login": "<PERSON><PERSON>", "logout": "Logout", "you_are_logged_as": "You are logged in as", "resend_the_code": "Resend the code", "have_an_account": "Do you have an account?", "enter_otp": "Enter the OTP sent to you", "forgot_password": "Forgot password", "reset_password": "Reset password", "password": "Password", "new_password": "New password", "confirm_password": "Confirm password", "confirm_new_password": "Confirm new password", "change_password": "Change password", "current_password": "Current password", "or": "Or", "menu": "<PERSON><PERSON>", "home_welcome": "Welcome to Timekeeping", "enter": "Enter", "select": "Select", "profile": "Profile", "information": "Information", "name": "Name", "full_name": "Full name", "date_of_birth": "Date of birth", "gender": "Gender", "male": "Male", "female": "Female", "phone_number": "Phone number", "active": "Active", "admin": "Administrator", "employees": "Employee list", "employee": "Employee", "create_employee": "Create new employee", "update_employee": "Update employee", "required_field": "This field is required", "must_be_email": "This must be an email", "minimum": "Minimum", "characters": "characters", "passwords_do_not_match": "Passwords do not match", "no_data": "No data", "work_schedule": "Work schedule", "company": "Company", "timekeeping_management": "Timekeeping management", "timekeeping_history": "Timekeeping history", "work_table": "Work table", "timekeeping_schedule_for_others": "Timekeeping schedule for others", "salary_management": "Salary management", "request_management": "Request management", "setting": "Settings", "general_setting": "General settings", "avatar": "Avatar", "actions": "Actions", "branch_list": "Branch list", "branch_name": "Branch name", "address": "Address", "grand_opening_date": "Grand opening date", "created_date": "Created date", "branch": "Branch", "add_new_branch": "Add new branch", "edit_branch": "Edit branch", "address_info": "Address information", "province_city": "Province/City", "district": "District", "ward": "Ward", "department": "Department", "department_list": "Department list", "department_name": "Department name", "standard_work_day": "Standard work day", "standard_work_hours": "Standard work hours", "activities": "Activities", "total_month_weekends": "Total month - (Sat, Sun)", "total_month_4": "Total month - 4", "add_new_department": "Add new department", "total_month_weekend_plus_2": "Total month - (Saturday, Sunday) + 2", "total_month_2": "Total month - 2", "total_month_sun": "Total month - Sun (Sat/2)", "total_month_days": "Total month - (Number of days)", "options": "Options", "number_of_days": "Number of days", "edit_department": "Edit department", "work_shift_list": "Work shift list", "shift_name": "Shift name", "shift_total_time": "Shift total time", "break_time": "Break time", "apply_date": "Apply date", "add_new_shift": "Add new shift", "personal_info": "Personal Information", "from": "From", "to": "To", "enter_start_end_time": "Enter start and end time to divide shifts (if any)", "time_allowance": "Time allowance", "lateness_allowance": "Lateness allowance (minutes)", "early_allowance": "Allowable early departure time (minutes)", "minute": "minutes", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "all": "All", "salary_coefficient": "Salary coefficient", "applied_branch": "Applied branch", "all_branches": "All branches", "applied_department": "Applied department", "work_shift": "Work shift", "edit_work_shift": "Edit work shift", "confirm": "Confirm", "delete_confirmation": "Are you sure you want to delete?", "success": "Success", "deleted": "Deleted", "added": "Added", "holiday": "Holiday", "holiday_list": "Holiday list", "holiday_name": "Holiday name", "description": "Description", "time_off": "Time off", "add_holiday": "Add new holiday", "edit_holiday": "Edit holiday", "time_apply": "Apply time", "allowance": "Allowance", "allowance_list": "Allowance list", "allowance_name": "Allowance name", "allowance_level": "Allowance level", "add_new_allowance": "Add new allowance", "edit_allowance": "Edit allowance", "position": "Position", "rank": "Rank", "add_new_position": "Add new position", "edit_position": "Edit position", "position_name": "Position name", "rank_from_highest_to_lowest": "Rank from highest to lowest, starting from 1", "role": "Role", "add_new_role": "Add new role", "edit_role": "Edit role", "role_name": "Role name", "permission": "Permission", "timekeeping_device": "Timekeeping device", "wifi_name": "Wifi name", "ip_address": "Ip address", "add_new_wifi_timekeeping": "Add new wifi timekeeping", "edit_wifi_timekeeping": "Edit wifi timekeeping", "wifi_timekeeping": "Wifi timekeeping", "take_leave": "Take leave", "leave_day": "leave day", "unapproved_leave": "Unapproved leave", "types_of_leave": "Types of leave", "leave_type_name": "Leave type name", "number_of_leave_days": "Number of leave days", "paid_leave": "Paid leave", "unpaid_leave": "Unpaid leave", "request": "Request", "updated": "Updated", "confirm_delete": "Are you want to delete", "ethnicity": "Ethnicity", "religion": "Religion", "place_of_birth": "Place of birth", "nationality": "Nationality", "health_status": "Health status", "marital_status": "Marital status", "contact_phone_number": "Contact phone number", "personal_documents": "Personal documents", "personal_information": "Personal information", "id_card_number": "ID card number", "issue_date": "Issue date", "issue_place": "Issue place", "id_card_with_chip": "ID card with chip", "permanent_address": "Permanent address", "temporary_address": "Temporary address", "detailed_address": "Detailed address", "education_information": "Education information", "education_level": "Education level", "major": "Main major", "educational_institution": "Educational institution", "training_duration": "Training duration", "other_majors": "Other majors", "certificates": "Certificates", "work_information": "Work information", "employment_status": "Employment status", "start_date": "Start date", "skip_timekeeping": "Skip timekeeping", "account_information": "Account information", "add_employee": "Add new employee", "edit_employee": "Edit employee", "employee_information": "Employee information", "labor_contract": "Labor contract", "contract_information": "Contract information", "contract_type": "Contract type", "contract_start_date": "Contract start date", "contract_end_date": "Contract end date", "position_salary": "Position salary P1", "competence_salary": "Competence salary P2", "performance_salary": "Performance salary P3", "tax": "Tax", "personal_income_tax": "Personal Income Tax", "tax_self_contribution_commitment": "Commitment to self-contribute tax", "company_contribution": "Company contribution", "dependents_count": "Number of dependents", "social_insurance_information": "Social insurance information", "social_insurance_number": "Social insurance number", "initial_healthcare_facility": "Initial healthcare facility", "bank_account_information": "Bank account information", "bank": "Bank", "bank_branch": "Bank branch", "beneficiary_name": "Beneficiary name", "account_number": "Account number", "tax_code": "Tax code", "valid_until": "Valid until", "no_expiration": "No expiration", "probation": "Probation", "temporary": "Temporary", "details": "Details", "activity_history": "Activity history", "working_status": "Working status", "currently_employed": "Currently employed", "resigned": "Resigned", "edit_labor_contract": "Edit labor contract", "healthy": "Healthy", "underlying_diseases": "Underlying diseases", "other": "Other", "high_school": "High school", "vocational_college": "Vocational college", "college": "College", "university": "University", "postgraduate": "Postgraduate", "position_allowance": "Position allowance", "responsibility_allowance": "Responsibility allowance", "hazardous_allowance": "Hazardous allowance", "seniority_allowance": "Seniority allowance", "mobility_allowance": "Mobility allowance", "attraction_allowance": "Attraction allowance", "other_allowance": "Other allowance", "married": "Married", "single": "Single", "widowed": "Widowed", "separated": "Separated", "divorced": "Divorced", "employee_code": "Employee code", "seniority": "Seniority", "yes": "Yes", "no": "No", "hourly_wage": "Hourly wage", "take_off_note": "Even when taking time off, the created salary coefficient still applies.", "working_schedule": "Company working schedule", "add_new_schedule": "Add new schedule", "applicable_employees": "Employees applied", "update_schedule_for_selected_employee": "Update the schedule for the selected employee in the current shift", "delete_and_apply_new_shift": "Delete the current shift and apply a new one", "edit_shift": "Edit shift", "edit_shift_day": "Edit shift day", "shift_details_for_day": "Shift details for the day", "shift_details": "Shift details", "custom": "Customization", "select_employee": "Select employee", "working_schedule_employee": "Employee work schedule", "notification_management": "Notification Management", "general_notification": "General Notification", "system_notification": "System Notification", "notification_name": "Notification Name", "create_general_notification": "Create General Notification", "attachment": "Attachment", "upload_file": "Upload File", "select_all": "Select All", "edit_general_notification": "Edit General Notification", "statistics": "Statistics", "view_timekeeping_table": "View Timekeeping Table", "actual_working_days": "Actual Working Days", "on_time": "On Time", "missing_hours": "Missing Hours", "timekeeping_for_others": "Timekeeping for Others", "business_trip": "Business Trip", "holiday_off": "Holiday Off", "day_off": "Day Off", "see_more": "See More", "edit_timekeeping_history": "Edit Timekeeping History", "check_in": "Check In", "check_out": "Check Out", "status": "Status", "monthly_timekeeping_table": "Monthly Timekeeping Table", "timekeeping_table": "Timekeeping Table", "standard_working_days": "Standard Working Days", "leave_days": "Leave Days", "overtime_hours": "Overtime Hours", "holiday_hours": "Holiday Hours", "late_hours": "Late Hours", "early_leave_hours": "Early Leave Hours", "total_working_days": "Total Working Days", "day": "Day", "hour": "Hour", "month": "Month", "request_information": "Request Information", "request_id": "Request ID", "request_type": "Request Type", "request_type_name": "Request type name", "reason": "Reason", "processed": "Processed", "leave_list": "Leave List", "overtime_list": "Overtime List", "employee_leave_list": "Employee Leave List", "leave_type": "Leave Type", "leave_permission_type": "Leave Permission Type", "time": "Time", "requester_information": "Requester Information", "leave_request": "Leave Request", "processed_status": "Processed", "personal_statistics": "Personal Statistics", "internal_notification": "Internal Notification", "overview": "Overview", "working_days": "Working Days", "timekeeping_for_others_count": "Timekeeping for Others Count", "leave_days_count": "Leave Days Count", "timekeeping": "Timekeeping", "timekeeping_wifi": "Timekeeping Wi-Fi", "timekeeping_success": "You have successfully clocked in", "timekeeping_late": "You clocked late in", "timekeeping_wrong_wifi": "You are not connected to the correct timekeeping Wi-Fi", "search": "Search", "sort_descending": "Sort in descending order", "submission_date": "Submission date", "attendance_request_details": "Attendance request details", "reject": "Reject", "approve": "Approve", "request_code": "Request code", "pending_approval": "Pending approval", "attendance": "Attendance", "attendance_type": "Attendance type", "rejected": "Rejected", "salary_advance_request_details": "Salary advance request details", "overtime_request_details": "Overtime request details", "overtime": "Overtime", "leave_request_details": "Leave Request Details", "business_trip_request_details": "Business trip request details", "information_update_request_details": "Information update request details", "update_information": "Update information", "create_request": "Create request", "with_permission": "With permission", "no_permission": "No permission", "advance_salary": "Advance salary", "advance_salary_count": "Advance salary", "unit": "Unit", "content_to_be_updated": "Content to be updated", "edit_request": "Edit request", "early_leave": "Early leave", "leave_early": "Leave early", "late": "Late", "early_return": "Late return", "late_and_leave_early": "Late and leave early", "import_data_from_excel": "Import data from Excel", "upload_excel": "Upload file excel", "download_file_template": "Download file template", "spreadsheet_name": "Spreadsheet name", "existing_data": "Existing data", "the_data_already_exists_or_the_field_is_valid": "The data already exists or the field is invalid", "imported_successfully": "Imported successfully", "field_is_duplicated": "Field is duplicated", "reason_rejected": "Reason for refusal", "request_details": "Request details", "personal_management": "Personal management", "cancel_request": "Cancel request", "reason_for_hiding_attendance_history": "Reason for hiding attendance history", "hidden_history_explanation": "Hidden history will not be counted and can be restored in “Hidden attendance history”", "attendance_history_hidden": "Attendance history hidden", "salary_table": "Salary Table", "salary_table_name": "Salary Table Name", "total_income": "Total Income", "total_deductions": "Total Deductions", "net_salary": "Net Salary", "income": "Income", "deductions": "Deductions", "additions": "Additions", "subtractions": "Subtractions", "total_net_salary": "Total net salary", "pending": "Pending", "approved": "Approved", "advance_list": "Advance list", "initial": "Initial", "create_salary_table": "Create salary table", "application_month": "Application month", "applicable_employee": "Applicable employee", "reason_for_deleting_attendance_history": "Reason for deleting attendance history", "deleted_history_explanation": "Deleted history will not be counted and can be restored in “Deleted attendance history”", "leave_information": "Leave information", "annual_leave_days": "Annual leave days", "sick_leave_days": "Sick leave days", "maternity_leave_days": "Maternity leave days", "wedding_leave_days": "Wedding leave days", "bereavement_leave_days": "Bereavement leave days", "annual_leave": "Annual leave", "sick_leave": "Sick leave", "maternity_leave": "Maternity leave", "marriage_leave": "Marriage leave", "bereavement_leave": "Bereavement leave", "leave_without_permission": "Leave without permission", "leave_without_permission_days": "Unpaid leave", "no_work_shift_selected": "No work shift selected", "select_work_shift": "Please select a work shift", "additional_amount": "Additional amout", "enter_additional_amounts_if_any": "Enter additional amounts if any", "enter_deductions_if_any": "Enter deductions if any", "error": "Error", "minutes": "minutes", "created": "Created", "sent": "<PERSON><PERSON>", "created_and_sent": "Created and sent", "send_salary_table": "Send salary table", "saved": "Saved", "deleted_timekeeping_history": "Deleted timekeeping history", "reason_for_deletion": "Reason for deletion", "timekeeping_history_restored": "Timekeeping history restored", "view_all": "View all", "select_branch": "Select branch", "employee_already_exists_or_email_not_valid": "Employee already exists or email not valid", "have_request": "Have request", "no_request": "Without request", "approval_date": "Approval date", "email": "Email", "document": "Document", "personal": "Personal", "shared": "Shared", "personal_document": "Personal Document", "folder": "Folder", "type": "Type", "upload": "Upload", "sharing": "Sharing", "not_shared": "Not Shared", "file": "File", "file_name": "File Name", "photo": "Photo", "audio": "Audio", "upload_folder": "Upload Folder", "create_new_folder": "Create New Folder", "rename": "<PERSON><PERSON>", "share": "Share", "download": "Download", "customize": "Customize", "show_less": "Show Less", "size": "Size", "shared_document": "Shared Document", "sharer": "Sharer", "shared_by": "Shared By", "contract": "Contract", "actual_received": "Actual received", "timekeeping_authentication": "Timekeeping authentication", "timkeeping_confirmation": "Timekeeping confirmation", "upload_authentication_photo": "Upload authentication photo", "authentic_photo": "Authentic photo", "company_statistics": "Company statistics", "total_employees": "Total employees", "employees_on_shift": "Employees on shift", "late_employees": "Late employees", "employees_not_on_shift": "Employees not on shift", "statistics_and_reports": "Statistics and reports", "staff_fluctuation": "Staff fluctuation", "staff_structure": "Staff structure", "whole_year": "Whole year", "contract_classification": "Contract classification", "business": "Business", "technical": "Technical", "design": "Design", "accounting": "Accounting", "fixed_term": "Fixed term", "indefinite_term": "Indefinite term", "seasonal": "Seasonal", "birthday": "Birthday", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "leave_holiday": "Leave (annual leave and holiday)", "total_salary": "Total salary", "total_allowance": "Total allowance", "total_bonus": "Total bonus", "salary_chart": "Salary chart", "amount_in_millions": "Amount (millions VND)", "salary_structure": "Salary structure", "notifications": "Notifications", "mark_all_read": "<PERSON> all read", "no_notification": "No notification", "stop_sharing": "Stop sharing", "folder_name": "Folder name", "no_document_shared": "No documents have been shared.", "contract_management": "Contract management", "contract_name": "Contract name", "customer": "Customer", "contract_value": "Contract value", "payment": "Payment", "upcoming_due": "Upcoming due", "still_valid": "Still valid", "initiated": "Initiated", "expired": "Expired", "effective_from": "Effective from", "effective_to": "Effective to", "create_contract": "Create contract", "signed_date": "Signed date", "renewal_reminder": "Renewal reminder (days)", "edit_contract": "Edit contract", "contract_info": "Contract information", "contract_code": "Contract code", "paid": "Paid", "unpaid": "Unpaid", "renewal_history": "Renewal history", "renewal_times": "Renewal times", "leave_management": "Leave Management", "edit_number_dayoff": "Edit number of days off", "reset_leave_confirmation": "Are you want to reset leave days?", "reset_leave_warning": "Leave days will be reset to 0 after resetting.", "update-employee": "<b>{name}</b> has updated information", "reset-leave": "<b>{name}</b> has reset leave days", "permanent_province_city": "Province/City (according to permanent address)", "permanent_district": "District (according to permanent address)", "permanent_ward": "Ward (according to permanent address)", "permanent_address_detail": "Address (according to permanent address)", "current_province_city": "Province/City (according to current address)", "current_district": "District (according to current address)", "current_ward": "Ward (according to current address)", "current_address_detail": "Address (according to current address)", "organizational_chart": "Organizational chart", "level": "Level", "hierarchical_ranking_with_level_1_being_the_highest": "Hierarchical ranking, with level 1 being the highest", "extend": "Extend", "contract_extension": "Contract extension", "all_files": "All files", "sort": "Sort", "latest": "Latest", "oldest": "Oldest", "name_from_a_to_z": "Name from A - Z", "name_from_z_to_a": "Name from Z - A", "upload_date": "Upload date", "upload_successful": "Upload successful", "upload_failed": "Upload failed", "path": "Đường dẫn", "last_modified": "Last modified", "all_documents": "All documents", "notice_period_due": "Notice period due", "password_changed_successfully": "Password changed successfully", "login_history": "Login history", "account_login_activity": "Account login activity", "this_device": "This device", "logout_successfully": "Logout successfully", "delete_shift_multiple": "Deleted {count} shifts", "confirm_delete_multiple_shift": "Are you want to delete {count} shifts?", "sum_salary": "Total salary", "role_admin": "Administrators have rights to manage the entire system", "overtime_name": "Overtime name", "overtime_type": "Overtime type", "create_overtime": "Create overtime", "normal_day_overtime": "Normal day overtime", "weekend_overtime": "Weekend overtime", "holiday_overtime": "Holiday overtime", "edit_overtime": "Edit overtime", "account_name": "Account name", "representative_name": "Representative name", "company_name": "Company name", "website": "Company website", "established_date": "Established date", "company_information": "Company information", "login_information": "Login information", "edit_company_information": "Edit company information", "forgot_password_description": " Enter the email address you used when you joined and we’ll send you OTP code to reset your password.", "all_roles": "All permissions", "view": "View", "permission_name": "Permission name", "sales_contract": "Sales contract", "service_contract": "Service contract", "business_cooperation_contract": "Business cooperation contract", "processing_contract": "Processing contract", "distribution_contract": "Distribution contract", "outsourcing_contract": "Outsourcing contract", "agency_contract": "Agency contract", "franchise_contract": "Franchise contract", "contract_type_name": "Contract type name", "create_contract_type": "Create contract type", "edit_contract_type": "Edit contract type", "manager": "Manager", "date_reset": "Permission reset date", "start": "Start", "message": "Message", "referral": "Referral", "invitation": "Invitation", "management": "Management", "login_with_referral": "Login with user referral", "campaign": "Campaign", "referral_invitation": "Referral invitation", "old_password": "Old password", "company_profile": "Company profile", "chatbot": "<PERSON><PERSON><PERSON>", "crm": "CRM", "user_referral": "User referral", "stop_bot": "Stop bot", "config": "Config", "created_at": "Created at", "phone": "Phone", "add_user": "Add user", "labels_management": "Labels Management", "fields_management": "Fields Management", "label": "Label", "enter_message": "Enter a message", "confirmation": "Confirmation", "signup": "Signup", "zalo": "<PERSON><PERSON>", "mail": "Mail"}, "button": {"detail": "Detail", "create": "Create", "edit": "Edit", "delete": "Delete", "no": "No", "yes": "Yes", "apply": "Apply", "back": "Back", "save": "Save", "add_new": "Add new", "filter": "Filter", "reset": "Reset", "clear_all": "Clear all", "cancel": "Cancel", "update": "Update", "continue": "Continue", "export_excel": "Export Excel", "import_excel": "Import Excel", "manual_addition": "Manual", "hide": "<PERSON>de", "create_and_send": "Create and send", "upload": "Upload", "export_pdf": "Export pdf", "only_view": "Only view", "invite": "Invite", "clear": "Clear", "recommended": "Recommended"}, "notifications": {"admin": {"create-employee": "{name} created employee {employee<PERSON>ame}", "update-employee": "{name} updated employee {employeeName}", "delete-employee": "{name} deleted employee {employee<PERSON><PERSON>}", "import-employee": "{name} imported employee", "create-branch": "{name} created branch {branchName}", "update-branch": "{name} updated branch {branchName}", "delete-branch": "{name} deleted branch {branchName}", "create-department": "{name} created department {departmentName}", "update-department": "{name} updated department {departmentName}", "delete-department": "{name} deleted department {departmentName}", "create-shift": "{name} created shift {shiftName}", "update-shift": "{name} updated shift {shiftName}", "delete-shift": "{name} deleted shift {shiftName}", "create-public-holiday": "{name} created holiday {holidayName}", "update-public-holiday": "{name} updated holiday {holidayName}", "delete-public-holiday": "{name} deleted holiday {holidayName}", "create-allowance": "{name} created allowance {allowanceName}", "update-allowance": "{name} updated allowance {allowanceName}", "delete-allowance": "{name} deleted allowance {allowanceName}", "create-position": "{name} created position {positionName}", "update-position": "{name} updated position {positionName}", "delete-position": "{name} deleted position {positionName}", "create-role": "{name} created role {roleName}", "update-role": "{name} updated role {roleName}", "delete-role": "{name} deleted role {roleName}", "create-wifi-attendance": "{name} created wifi attendance {wifiName}", "update-wifi-attendance": "{name} updated wifi attendance {wifiName}", "delete-wifi-attendance": "{name} deleted wifi attendance {wifiName}", "create-work-schedule": "{name} created work schedule", "update-work-schedule": "{name} updated work schedule for {employeeName}", "update-work-schedule-list": "{name} updated work schedule for the company", "delete-work-schedule": "{name} deleted work schedule", "create-salary": "{name} created salary table {salaryTime}", "update-salary": "{name} updated salary table {salaryTime} for {employeeName}", "delete-salary": "{name} deleted salary table {salaryTime} for {employeeName}", "send-salary": "{name} sent salary table {salaryTime}", "approve-salary": "{name} approved salary table {salaryTime}", "reject-salary": "{name} rejected salary table {salaryTime}", "create-form": "{name} created request {formName} for {employeeName}", "update-form": "{name} updated request {formName} for {employeeName}", "delete-form": "{name} deleted request {formName} for {employeeName}", "approve-form": "{name} approved request {formName} for {employeeName}", "reject-form": "{name} rejected request {formName} for {employeeName}", "create-news": "{name} created news {newsName}", "update-news": "{name} updated news {newsName}", "delete-news": "{name} deleted news {newsName}", "update-history": "{name} updated attendance history for {employeeName}", "delete-history": "{name} deleted attendance history for {employeeName}", "create-contract": "{name} created contract {contractName}", "update-contract": "{name} updated contract {contractName}", "delete-contract": "{name} deleted contract {contractName}", "reset-leave": "{name} reset leave days", "restore-history": "{name} restored attendance history for {employeeName}", "contract-expire": "{contractName} is about to expire", "extend-contract": "{contractName} been extended", "create-overtime": "{name} created overtime {overtimeName}", "update-overtime": "{name} updated overtime {overtimeName}", "delete-overtime": "{name} deleted overtime {overtimeName}", "create-contract-type": "{name} created contract type {contractTypeName}", "update-contract-type": "{name} updated contract type {contractTypeName}", "delete-contract-type": "{name} deleted contract type {contractTypeName}"}, "employee": {"update-employee-me": "{name} updated your information", "create-form-me": "{name} created request {formName} for you", "update-form-me": "{name} updated request {formName} for you", "delete-form-me": "{name} deleted request {formName} for you", "approve-form-me": "{name} approved request {formName} for you", "reject-form-me": "{name} rejected request {formName} for you", "create-news-me": "{name} created news {newsName} for you", "update-news-me": "{name} updated news {newsName} for you", "delete-news-me": "{name} deleted news {newsName} for you", "send-salary-me": "{name} sent salary table {salaryTime} for you", "update-work-schedule-me": "{name} updated work schedule for you", "update-history-me": "{name} updated attendance history for you", "delete-history-me": "{name} deleted attendance history for you", "cancel-form-me": "{name} canceled request {formName}", "share-file-me": "{name} shared a file with you", "share-folder-me": "{name} shared a folder with you", "restore-history-me": "{name} restored attendance history for you"}}, "roles": {"employee": "Employee management", "salary": "Salary management", "department": "Department management", "allowance": "Allowance management", "wifi_attendance": "Attendance wifi management", "work_schedule": "Work schedule management", "form": "Request management", "shift": "Shift management", "position": "Position management", "history_system": "Timekeeping management", "branch": "Branch management", "public_holiday": "Public holiday management", "role": "Role management", "news": "Announcement management", "contract": "Contract management", "leave": "Leave management", "form_type": "Request type management", "company": "Company information", "overtime": "Overtime management", "analytic": "Company analytics", "contract_type": "Contract type management", "view_employee": "View employee", "create_employee": "Create employee", "update_employee": "Update employee", "delete_employee": "Delete employee", "view_salary": "View salary management", "create_salary": "Create salary management", "update_salary": "Update salary management", "delete_salary": "Delete salary management", "view_department": "View department", "create_department": "Create department", "update_department": "Update department", "delete_department": "Delete department", "view_allowance": "View allowance", "create_allowance": "Create allowance", "update_allowance": "Update allowance", "delete_allowance": "Delete allowance", "view_wifi_attendance": "View wifi attendance", "create_wifi_attendance": "Create wifi attendance", "update_wifi_attendance": "Update wifi attendance", "delete_wifi_attendance": "Delete wifi attendance", "view_work_schedule": "View work schedule", "create_work_schedule": "Create work schedule", "update_work_schedule": "Update work schedule", "delete_work_schedule": "Delete work schedule", "view_form": "View request", "create_form": "Create request", "update_form": "Update request", "delete_form": "Delete request", "approve_form": "Approve request", "view_shift": "View shift", "create_shift": "Create shift", "update_shift": "Update shift", "delete_shift": "Delete shift", "view_position": "View position", "create_position": "Create position", "update_position": "Update position", "delete_position": "Delete position", "view_history_system": "View attendance management", "create_history_system": "Create attendance management", "update_history_system": "Update attendance management", "delete_history_system": "Delete attendance management", "view_branch": "View branch", "create_branch": "Create branch", "update_branch": "Update branch", "delete_branch": "Delete branch", "view_public_holiday": "View public holiday", "create_public_holiday": "Create public holiday", "update_public_holiday": "Update public holiday", "delete_public_holiday": "Delete public holiday", "view_role": "View role", "create_role": "Create role", "update_role": "Update role", "delete_role": "Delete role", "view_news": "View general notification", "create_news": "Create general notification", "update_news": "Update general notification", "delete_news": "Delete general notification", "view_day_off": "View type of leave", "view_form_type": "View type of request", "view_analytic": "View company statistics", "create_contract": "Create contract", "view_contract": "View contract", "update_contract": "Update contract", "delete_contract": "Delete contract", "view_analytic_salary": "View analytic salary", "view_leave": "View leave days", "update_leave": "Update leave days"}, "error": {"BAD_REQUEST": "Invalid request error, please check", "EMAIL_EXIST": "Email already exists", "DUPLICATE_WORK_SCHEDULE": "Duplicate work schedule", "NUMBER_OF_LEAVES_INVALID": "The number of days off exceeds the number of available days off", "WORK_DATE_HAS_PASSED": "Work date has passed", "START_WORK_TIME_TAKEN": "Start work time is taken", "TIME_IN_SHIFT": "The time is during the shift", "OLD_PASSWORD_NOT_MATCH": "Old password not match", "FOLDER_NOT_EXIST": "Document does not exist", "PERMISSION_ACCESS_DENIED": "You do not have access to this doucment", "IS_AUTHOR_FOLDER": "You are the owner of this document", "SHIFT_HAS_PASSED": "Shifts cannot be changed because the shift is starting", "ROLE_EXIST_IN_EMPLOYEE": "There exists an employee currently assigned to this role"}, "home": {"MỘT NỀN TẢNG - ĐA KÊNH - TỐI ĐA HIỆU QUẢ TUYỂN SINH": "ONE PLATFORM – MULTI-CHANNEL – MAXIMIZED ENROLLMENT EFFICIENCY", "Tối Ưu Tuyển Sinh - Vận Hành Đa Kênh Hiệu Quả": "Streamlined Admissions – Powering Effective Multi-Channel Engagement", "Chọn giải pháp phù hợp với mọi quy mô trung tâm và trường học": "Choose a solution that fits institutions of all sizes – from a solid starting point to comprehensive enrollment management. Maple STC helps you accelerate your processes, offer 24/7 counseling, manage student profiles, and intelligently connect students with schools through smart automation.", "Bắt đầu ngay": "Get Started", "Chatbot thông minh - Tư vấn 24/7": "<PERSON>bot – 24/7 Support", "Tăng tốc tuyển sinh với chatbot AI hỗ trợ tư vấn mọi lúc": "Accelerate Enrollment with an AI Chatbot That Supports Students Anytime, Anywhere. Automatically receive and process student information, sync data in real-time, and empower your team to focus on deeper, more impactful counseling sessions.", "Chatbot hỗ trợ tư vấn tuyển sinh 24/7": "Your Always-Available Admissions Consultant", "Tích hợp trực tiếp với website, fanpage và các kênh tuyển sinh số": "Direct Integration with Website, Fanpage, and Digital Enrollment Channels", "Có khả năng học hành vi người dùng và điều chỉnh câu trả lời dựa trên lịch sử tương tác": "Capable of Learning User Behavior and Adapting Responses Based on Interaction History", "Nhân viên có thể theo dõi lịch sử trò chuyện và phối hợp hỗ trợ chuyên sâu": "Staff Can Track Chat History and Collaborate for In-Depth Support", "Tự động thu thập dữ liệu từ học sinh trực tiếp qua tin nhắn - nhanh chóng, không cần điền biểu mẫu": "Automatically Collects Student Data Directly Through Messages – Fast and Form-Free", "CRM - Quản lý tuyển sinh chuyên nghiệp": "CRM – Professional Enrollment Management", "Tối ưu quy trình chăm sóc học sinh với hệ thống CRM mạnh mẽ: quản lý lead hiệu quả": "Optimize Your Student Engagement Process with a Powerful CRM System: Efficient lead management, centralized consultation history, flexible user permissions, and real-time data updates from the chatbot — all in one platform that’s intuitive, synchronized, and easy to deploy.", "Tùy biến luồng tư vấn sale": "Customizable Sales Consultation Flows", "Thiết lập quy trình chăm sóc học sinh theo từng chiến dịch": "Set Up Student Engagement Workflows by Campaign and Stage. Freely categorize, label, and track the effectiveness of your counseling efforts.", "Phân quyền không giới hạn nhân sự": "Unlimited User Permissions Management", "Thêm bao nhiêu người dùng tùy ý": "Add Unlimited Users. Assign Flexible Roles for Seamless Collaboration Across Teams.", "Ghi chú và lưu trữ nội dung tư vấn": "Note and Store Counseling Content", "Lưu lại mọi trao đổi với học sinh theo từng hồ sơ": "Keep a Record of All Student Interactions by Profile – Ensuring No Information Is Missed and Making Follow-Ups Easy.", "Tự động đồng bộ thông tin từ chatbot": "Automatically Sync Information from the Chatbot", "Toàn bộ dữ liệu thu thập từ chatbot được đồng bộ về CRM ngay tức thì": "All Data Collected from the Chatbot Is Instantly Synced to the CRM – No Manual Input, No Duplicates.", "Cho phép cập nhật thông tin cần thu thập liên tục theo thời gian thực": "Allows Real-Time Updates to the Information Collected", "Thông tin từ học sinh được ghi nhận và hiển thị tức thì": "Student information is captured and displayed instantly, ensuring your counseling team always works with the most up-to-date data – never miss an opportunity.", "SmartApply – Tối ưu hồ sơ & quy trình nhập học": "SmartApply – Optimize Application Profiles & Enrollment Process", "Quản lý hồ sơ và quy trình nhập học chưa bao giờ dễ dàng đến thế": "Managing Student Profiles and Enrollment Processes Has Never Been Easier. With SmartApply, students and schools can track progress in real time, stay closely connected, and personalize the entire journey to match individual needs.", "Theo dõi hồ sơ online thời gian thực": "Real-Time Online Profile Tracking", "Tạo, cập nhật và theo dõi tiến độ hồ sơ mọi lúc, mọi nơi": "Create, Update, and Track Application Progress Anytime, Anywhere", "Kết nối hiệu quả với đối tác tuyển sinh": "Connect Effectively with Recruitment Partners", "Liên kết chặt chẽ giữa học sinh, trường học và tư vấn viên": "Seamless Connection Between Students, Schools, and Advisors", "Tích hợp API với phần mềm tuyển sinh của trường": "API Integration with Your School’s Enrollment System", "Đồng bộ dữ liệu nhanh chóng, giảm thao tác thủ công": "Fast Data Synchronization, Reduced Manual Work.", "Tùy biến quy trình nhập học": "Customizable Enrollment Process", "Thiết kế lộ trình theo mô hình riêng của từng trường": "Design Enrollment Journeys Based on Each School’s Unique Model.", "Dữ liệu bảo mật cao, truy cập dễ dàng trên mọi thiết bị": "Highly Secure Data, Easily Accessible on Any Device", "Dữ liệu lưu trữ an toàn trên Cloud S3": "Data Securely Stored on S3 Cloud – Flexible and Efficient Information Retrieval", "Real-time data sync with Webhooks": "Real-time data sync with Webhooks", "Kết nối Webhook để nhận thông báo tức thì từ các nền tảng bên ngoài": "Connect Webhooks to Receive Instant Notifications from External Platforms – No More Manual Checking.", "Company": "Company"}, "pricing": {"title": "MULTI-CHAN<PERSON><PERSON> ENROLLMENT SOLUTION", "subtitle": "Choose the right package - Increase enrollment efficiency - Optimize processes", "plans": {"basic": {"name": "Basic", "subtitle": "Solid Foundation", "price": "$3,800", "currency": "USD/year", "period": "Includes all basic features"}, "eco": {"name": "Eco", "subtitle": "Comprehensive Solution", "price": "$4,800", "currency": "USD/year", "period": "Includes all advanced features"}, "pro": {"name": "Pro", "subtitle": "Complete Optimization", "price": "$6,000", "currency": "USD/year", "period": "Includes all premium features"}}, "sections": {"chatbot": "Smart Chatbot - 24/7 Support", "crm": "CRM - Professional Enrollment Management", "smartapply": "SmartApply - Optimize Profiles & Admission Process"}, "features": {"chatbot": {"integration": "Integration with websites, fanpages and other enrollment channels", "customization": "Can be customized according to specific requirements of each center and school", "instant_response": "Receive hundreds of questions and answer instantly", "registration_support": "Support registration information of students enrolling at centers and schools"}, "crm": {"lead_management": "Create leads - manage workflow", "role_permissions": "Role-based staff permissions", "professional_process": "Professional enrollment process management", "detailed_reports": "Provide detailed reports on enrollment status over time", "auto_sync": "Automatically sync chatbot student information", "notification_channels": "Register to receive notifications through channels"}, "smartapply": {"profile_management": "Create, update student profiles and track online profile progress"}, "advanced": {"third_party_api": "Third-party partners, APIs - Academic system integration", "system_integration": "Connect with school academic systems, helping to synchronize student data", "webhook_connection": "Webhook - API connection with system", "webhook_creation": "Create webhooks for APIs during chatbot operation", "webhook_setup": "Webhook setup"}}, "limitations": {"title": "Package limitations:", "applications": "Number of free profiles:", "setup_fee": "Fee per profile if exceeding quantity:", "support_fee": "Annual maintenance fee:"}, "notes": {"title": "Important notes:", "basic": "Suitable for small-scale learning centers and schools that need basic solutions to automate enrollment processes.", "eco": "For medium-scale centers that need effective enrollment management. Integration with existing academic systems. Automate processes from counseling to enrollment.", "pro": "For large schools and educational corporations. Comprehensive integration with existing systems. Multi-channel support and complete automation of A-Z enrollment processes with detailed reporting."}}}