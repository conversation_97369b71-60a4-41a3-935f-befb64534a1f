{"common": {"register": "<PERSON><PERSON><PERSON> ký", "login": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "you_are_logged_as": "Bạn đang đăng nhập với tư cách:", "resend_the_code": "<PERSON><PERSON><PERSON> lại mã", "have_an_account": "Bạn đã có tài khoản chưa?", "enter_otp": "Nhập OTP đã gửi cho bạn", "forgot_password": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "reset_password": "Đặt lại mật khẩu", "password": "<PERSON><PERSON><PERSON>", "new_password": "<PERSON><PERSON><PERSON> mới", "confirm_password": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "confirm_new_password": "<PERSON><PERSON><PERSON><PERSON> lại mật khẩu mới", "change_password": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "current_password": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "or": "Hoặc", "menu": "<PERSON><PERSON>", "home_welcome": "Chào mừng đến với Timekeeping", "enter": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON> sơ", "information": "Thông tin", "name": "<PERSON><PERSON><PERSON>", "full_name": "Họ và tên", "date_of_birth": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "male": "Nam", "female": "<PERSON><PERSON>", "phone_number": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "admin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "employees": "<PERSON><PERSON> s<PERSON>ch nhân viên", "employee": "Nhân viên", "create_employee": "<PERSON><PERSON>o mới nhân viên", "update_employee": "<PERSON><PERSON><PERSON> nh<PERSON>t nhân viên", "required_field": "Trư<PERSON>ng này là bắ<PERSON> buộc", "must_be_email": "<PERSON><PERSON>y phải là một email", "minimum": "<PERSON><PERSON><PERSON> thi<PERSON>u", "characters": "ký tự", "passwords_do_not_match": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "no_data": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "work_schedule": "<PERSON><PERSON><PERSON> l<PERSON> vi<PERSON>", "company": "<PERSON><PERSON>ng ty", "timekeeping_management": "<PERSON><PERSON><PERSON><PERSON> lý chấm công", "timekeeping_history": "<PERSON><PERSON><PERSON> sử chấm công", "work_table": "<PERSON><PERSON><PERSON> công", "timekeeping_schedule_for_others": "<PERSON><PERSON><PERSON> chấm công hộ", "salary_management": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "request_management": "<PERSON><PERSON><PERSON><PERSON> lý yêu cầu", "setting": "Cài đặt", "general_setting": "<PERSON>ài đặt chung", "avatar": "Ảnh đại diện", "actions": "<PERSON><PERSON>", "branch_list": "<PERSON><PERSON> s<PERSON>ch chi nh<PERSON>h", "branch_name": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "address": "Địa chỉ", "grand_opening_date": "<PERSON><PERSON><PERSON> tr<PERSON>", "created_date": "<PERSON><PERSON><PERSON>", "branch": "<PERSON> n<PERSON>h", "add_new_branch": "<PERSON><PERSON><PERSON><PERSON> mới chi nh<PERSON>h", "edit_branch": "Chỉnh sửa chi nh<PERSON>h", "address_info": "Thông tin địa chỉ", "province_city": "Tỉnh/Thành phố", "district": "Quận/Huyện", "ward": "Phường/Xã", "department": "Phòng ban", "department_list": "<PERSON><PERSON> s<PERSON>ch phòng ban", "department_name": "<PERSON>ên phòng ban", "standard_work_day": "<PERSON><PERSON><PERSON>", "standard_work_hours": "<PERSON><PERSON><PERSON> công chu<PERSON>n", "activities": "<PERSON><PERSON><PERSON> đ<PERSON>", "total_month_weekends": "<PERSON><PERSON><PERSON> tháng - (T7, CN)", "total_month_4": "Tổng tháng - 4", "add_new_department": "<PERSON>h<PERSON><PERSON> mới phòng ban", "total_month_weekend_plus_2": "<PERSON><PERSON><PERSON> tháng - (<PERSON><PERSON><PERSON> 7, <PERSON><PERSON>) + 2", "total_month_2": "Tổng tháng - 2", "total_month_sun": "<PERSON><PERSON><PERSON> tháng - CN (T7/2)", "total_month_days": "<PERSON><PERSON><PERSON> tháng - (<PERSON><PERSON>)", "options": "<PERSON><PERSON><PERSON>", "number_of_days": "<PERSON><PERSON> ng<PERSON>y", "edit_department": "Chỉnh sửa phòng ban", "work_shift_list": "<PERSON><PERSON> s<PERSON>ch ca làm việc", "shift_name": "<PERSON><PERSON><PERSON> ca làm việc", "shift_total_time": "<PERSON><PERSON><PERSON><PERSON> gian tổng ca", "break_time": "Thời gian nghỉ giữa ca", "apply_date": "<PERSON><PERSON><PERSON>", "add_new_shift": "<PERSON><PERSON><PERSON><PERSON> mới ca làm việc", "personal_info": "Thông tin cá nhân", "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "enter_start_end_time": "<PERSON><PERSON><PERSON><PERSON> thời điểm bắt đầu và kết thúc để chia ca (nếu có)", "time_allowance": "<PERSON>h<PERSON><PERSON> gian cho phép chấm công", "lateness_allowance": "<PERSON>h<PERSON><PERSON> gian cho phép đi tr<PERSON> (phút)", "early_allowance": "<PERSON>h<PERSON><PERSON> gian cho phép về sớm (phút)", "minute": "<PERSON><PERSON><PERSON><PERSON>", "monday": "Thứ 2", "tuesday": "Thứ 3", "wednesday": "Thứ 4", "thursday": "Thứ 5", "friday": "Thứ 6", "saturday": "Thứ 7", "sunday": "<PERSON>ủ <PERSON>h<PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "salary_coefficient": "<PERSON><PERSON> s<PERSON> l<PERSON>", "applied_branch": "<PERSON>h <PERSON> dụng", "all_branches": "<PERSON><PERSON><PERSON> cả chi nh<PERSON>h", "applied_department": "Phòng ban <PERSON> dụng", "work_shift": "<PERSON>a làm việc", "edit_work_shift": "Chỉnh sửa ca làm việc", "confirm": "<PERSON><PERSON><PERSON>", "delete_confirmation": "Bạn có chắc chắn muốn xóa", "success": "<PERSON><PERSON><PERSON><PERSON> công", "deleted": "Đã xóa", "added": "<PERSON><PERSON> thêm mới", "holiday": "<PERSON><PERSON><PERSON>", "holiday_list": "<PERSON><PERSON> s<PERSON>ch các ng<PERSON>", "holiday_name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "time_off": "Nghỉ làm", "add_holiday": "<PERSON><PERSON><PERSON><PERSON> mới ng<PERSON>y lễ", "edit_holiday": "Chỉnh s<PERSON>a ng<PERSON>y lễ", "time_apply": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> d<PERSON>", "allowance": "<PERSON><PERSON> cấp", "allowance_list": "<PERSON><PERSON> s<PERSON>ch phụ cấp", "allowance_name": "<PERSON><PERSON><PERSON> ph<PERSON> cấp", "allowance_level": "<PERSON><PERSON><PERSON> p<PERSON> cấp", "add_new_allowance": "<PERSON><PERSON><PERSON><PERSON> mới phụ cấp", "edit_allowance": "Chỉnh sửa phụ cấp", "position": "<PERSON><PERSON><PERSON> v<PERSON>", "rank": "<PERSON><PERSON><PERSON> h<PERSON>ng", "add_new_position": "<PERSON><PERSON><PERSON><PERSON> mới chức vụ", "edit_position": "Chỉnh s<PERSON>a chức vụ", "position_name": "<PERSON><PERSON><PERSON> v<PERSON>", "rank_from_highest_to_lowest": "<PERSON><PERSON><PERSON> hạng từ cao đến thấp, b<PERSON><PERSON> đầu từ 1", "role": "<PERSON>ai trò", "add_new_role": "<PERSON>hê<PERSON> mới vai trò", "edit_role": "Chỉnh sửa vai trò", "role_name": "<PERSON>ên vai trò", "permission": "<PERSON><PERSON><PERSON><PERSON> hạn", "timekeeping-device": "<PERSON><PERSON><PERSON><PERSON> bị chấm công", "wifi_name": "<PERSON><PERSON>n wifi", "ip_address": "Địa chỉ IP", "add_new_wifi_timekeeping": "<PERSON>hêm mới wifi chấm công", "edit_wifi_timekeeping": "Chỉnh sửa wifi chấm công", "wifi_timekeeping": "<PERSON>ifi chấm công", "take_leave": "Nghỉ phép", "leave_day": "ng<PERSON>y ph<PERSON>p", "types_of_leave": "Loại nghỉ phép", "leave_type_name": "<PERSON><PERSON>n lo<PERSON>i nghỉ phép", "number_of_leave_days": "<PERSON><PERSON> ngày nghỉ", "paid_leave": "<PERSON><PERSON>", "unpaid_leave": "<PERSON><PERSON><PERSON><PERSON> lư<PERSON>", "request": "<PERSON><PERSON><PERSON> c<PERSON>", "updated": "<PERSON><PERSON> cập nh<PERSON>t", "confirm_delete": "Bạn có chắc chắn muốn xóa", "ethnicity": "<PERSON><PERSON> t<PERSON>c", "religion": "Tôn gi<PERSON>o", "place_of_birth": "<PERSON><PERSON><PERSON>", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "health_status": "<PERSON>ình trạng sức khỏe", "marital_status": "Tình trạng hôn nhân", "contact_phone_number": "<PERSON><PERSON> điện thoại liên hệ", "personal_documents": "<PERSON><PERSON><PERSON><PERSON> tờ cá nhân", "personal_information": "Thông tin cá nhân", "id_card_number": "Số CCCD", "issue_date": "<PERSON><PERSON><PERSON> c<PERSON>", "issue_place": "<PERSON><PERSON><PERSON> c<PERSON>p", "id_card_with_chip": "CCCD có gắn chip", "permanent_address": "Địa chỉ thường trú", "temporary_address": "Địa chỉ tạm trú", "detailed_address": "Đ<PERSON><PERSON> chỉ chi tiết", "education_information": "<PERSON><PERSON><PERSON><PERSON> tin học vấn", "education_level": "<PERSON><PERSON><PERSON><PERSON> độ học vấn", "major": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>", "educational_institution": "Cơ sở đào tạo", "training_duration": "<PERSON>h<PERSON><PERSON> gian đào tạo", "other_majors": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>", "certificates": "Chứng chỉ năng lực", "work_information": "Thông tin công việc", "employment_status": "<PERSON><PERSON><PERSON><PERSON> thái làm việc", "start_date": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "skip_timekeeping": "Bỏ qua chấm công", "account_information": "Thông tin tài k<PERSON>n", "add_employee": "<PERSON>h<PERSON><PERSON> mới nhân viên", "edit_employee": "Chỉnh sửa nhân viên", "employee_information": "Thông tin nhân viên", "labor_contract": "<PERSON><PERSON><PERSON> đồng lao động", "contract_information": "<PERSON><PERSON><PERSON><PERSON> tin hợp đồng", "contract_type": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "contract_start_date": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> hợp đồng", "contract_end_date": "<PERSON><PERSON><PERSON> kết thúc hợp đồng", "position_salary": "Lương vị trí P1", "competence_salary": "Lương năng lực P2", "performance_salary": "<PERSON><PERSON><PERSON><PERSON> hiệu suất P3", "tax": "<PERSON><PERSON><PERSON>", "personal_income_tax": "T<PERSON>ế TNCN", "tax_self_contribution_commitment": "<PERSON> kết tự đóng thuế", "company_contribution": "<PERSON><PERSON><PERSON> ty đóng", "dependents_count": "<PERSON><PERSON> ng<PERSON>ời phụ thuộc", "social_insurance_information": "Thông tin BHXH", "social_insurance_number": "Số BHXH", "initial_healthcare_facility": "Nơi KCB ban đầu", "bank_account_information": "Thông tin tài khoản ngân hàng", "bank": "<PERSON><PERSON> h<PERSON>", "bank_branch": "<PERSON> nh<PERSON>h ngân hàng", "beneficiary_name": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thụ hưởng", "account_number": "Số tài <PERSON>n", "tax_code": "<PERSON><PERSON> số thuế", "valid_until": "<PERSON><PERSON> thời hạn", "no_expiration": "<PERSON><PERSON><PERSON><PERSON> thời hạn", "probation": "<PERSON><PERSON><PERSON> vi<PERSON>c", "temporary": "Thời vụ", "details": "<PERSON> ti<PERSON>", "activity_history": "<PERSON><PERSON><PERSON> sử hoạt động", "working_status": "<PERSON>h<PERSON>ng tin làm việc", "currently_employed": "<PERSON><PERSON> làm vi<PERSON>c", "resigned": "Đã nghỉ việc", "edit_labor_contract": "Chỉnh sửa hợp đồng lao động", "healthy": "Khỏe mạnh", "underlying_diseases": "<PERSON><PERSON> b<PERSON>n", "other": "K<PERSON><PERSON><PERSON>", "high_school": "<PERSON>rung học phổ thông", "vocational_college": "<PERSON>rung cấp nghề", "college": "<PERSON> đẳng", "university": "<PERSON><PERSON><PERSON>", "postgraduate": "<PERSON>", "position_allowance": "<PERSON><PERSON> cấp chức <PERSON>, chứ<PERSON> danh", "responsibility_allowance": "<PERSON><PERSON> cấp tr<PERSON><PERSON>", "hazardous_allowance": "<PERSON><PERSON> cấ<PERSON> đ<PERSON>, ng<PERSON>, nặng nhọc", "seniority_allowance": "<PERSON><PERSON> cấp thâm ni<PERSON>n", "mobility_allowance": "<PERSON><PERSON> cấp lư<PERSON> đ<PERSON>", "attraction_allowance": "<PERSON><PERSON> cấp thu hút", "other_allowance": "<PERSON><PERSON> c<PERSON>p k<PERSON>c", "married": "<PERSON><PERSON> kết hôn", "single": "<PERSON><PERSON><PERSON> thân", "widowed": "Góa", "separated": "<PERSON>y thân", "divorced": "<PERSON><PERSON> <PERSON>ôn", "employee_code": "Mã nhân viên", "seniority": "<PERSON><PERSON><PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "hourly_wage": "<PERSON><PERSON><PERSON><PERSON> theo gi<PERSON>", "take_off_note": "Nghỉ làm vẫn áp dụng hệ số lương đã tạo", "working_schedule": "<PERSON><PERSON><PERSON> làm vi<PERSON><PERSON> công ty", "add_new_schedule": "<PERSON><PERSON><PERSON><PERSON> mới lịch làm vi<PERSON>c", "applicable_employees": "<PERSON><PERSON><PERSON> viên <PERSON> dụng", "update_schedule_for_selected_employee": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lịch làm việc của nhân viên đư<PERSON><PERSON> chọn trong ca hiện tại", "delete_and_apply_new_shift": "Xóa ca làm việc hiện tại và áp dụng ca làm việc mới", "edit_shift": "Chỉnh sửa ca làm việc", "edit_shift_day": "Chỉnh sửa ca làm việc ngày", "shift_details_for_day": "<PERSON> ti<PERSON>t lịch làm vi<PERSON><PERSON> ng<PERSON>y", "shift_details": "<PERSON> ti<PERSON>t lịch làm vi<PERSON>c", "custom": "<PERSON><PERSON><PERSON> chỉnh", "select_employee": "<PERSON><PERSON><PERSON> nhân viên", "working_schedule_employee": "<PERSON><PERSON><PERSON> làm vi<PERSON><PERSON> nhân viên", "notification_management": "<PERSON><PERSON><PERSON><PERSON> lý thông báo", "general_notification": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> chung", "system_notification": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống", "notification_name": "<PERSON><PERSON><PERSON> thông báo", "create_general_notification": "<PERSON><PERSON><PERSON> thông b<PERSON>o chung", "attachment": "<PERSON><PERSON><PERSON>", "upload_file": "<PERSON><PERSON><PERSON> lên", "select_all": "<PERSON><PERSON><PERSON> tất cả", "edit_general_notification": "Chỉnh sửa thông b<PERSON>o chung", "statistics": "<PERSON><PERSON><PERSON><PERSON> kê", "view_timekeeping_table": "<PERSON><PERSON> b<PERSON> công", "actual_working_days": "<PERSON><PERSON><PERSON> công thực tế", "on_time": "<PERSON><PERSON><PERSON> gi<PERSON>", "missing_hours": "<PERSON><PERSON><PERSON><PERSON> giờ", "timekeeping_for_others": "<PERSON><PERSON><PERSON> công hộ", "business_trip": "<PERSON><PERSON><PERSON> t<PERSON>c", "holiday_off": "Nghỉ lễ", "day_off": "Nghỉ làm", "see_more": "<PERSON><PERSON>", "edit_timekeeping_history": "Chỉnh sửa lịch sử chấm công", "check_in": "Vào ca", "check_out": "<PERSON> ca", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "monthly_timekeeping_table": "<PERSON><PERSON><PERSON> công tháng", "timekeeping_table": "<PERSON><PERSON><PERSON> công", "standard_working_days": "<PERSON><PERSON><PERSON>", "leave_days": "Ngày nghỉ phép", "unapproved_leave": "Ngày nghỉ không phép", "overtime_hours": "G<PERSON>ờ công tăng ca", "holiday_hours": "<PERSON><PERSON><PERSON> công ngày lễ", "late_hours": "Số giờ đi muộn", "early_leave_hours": "Số giờ về sớm", "total_working_days": "<PERSON><PERSON><PERSON> ng<PERSON>y công", "day": "ng<PERSON>y", "hour": "giờ", "month": "<PERSON><PERSON><PERSON><PERSON>", "request_information": "<PERSON>h<PERSON>ng tin yêu cầu", "request_id": "<PERSON><PERSON> đơn", "request_type": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "request_type_name": "<PERSON><PERSON><PERSON> lo<PERSON>i yêu c<PERSON>u", "reason": "Lý do", "processed": "Đã xử lý", "leave_list": "<PERSON><PERSON> s<PERSON>ch nghỉ", "overtime_list": "<PERSON><PERSON> s<PERSON>ch tăng ca", "employee_leave_list": "<PERSON>h s<PERSON>ch nhân viên nghỉ ngày", "leave_type": "Loại nghỉ", "leave_permission_type": "Loại nghỉ phép", "time": "<PERSON><PERSON><PERSON><PERSON> gian", "requester_information": "Thông tin người yêu cầu", "leave_request": "Xin nghỉ", "processed_status": "Đã xử lý", "personal_statistics": "<PERSON>h<PERSON><PERSON> kê cá nhân", "internal_notification": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o nội bộ", "overview": "<PERSON><PERSON><PERSON> quan", "working_days": "<PERSON><PERSON><PERSON> l<PERSON> vi<PERSON>c", "timekeeping_for_others_count": "<PERSON><PERSON><PERSON> công hộ", "leave_days_count": "Ngày nghỉ", "timekeeping": "<PERSON><PERSON><PERSON> công", "timekeeping_wifi": "<PERSON>ifi chấm công", "timekeeping_success": "Bạn đã chấm công thành công", "timekeeping_late": "Bạn đã chấm công muộn", "timekeeping_wrong_wifi": "Bạn chưa kết nối đúng wifi chấm công", "search": "<PERSON><PERSON><PERSON>", "sort_descending": "<PERSON><PERSON><PERSON> xếp theo thứ tự từ cao đến thấp", "submission_date": "<PERSON><PERSON><PERSON>", "attendance_request_details": "<PERSON> tiết yêu cầu chấm công", "reject": "<PERSON><PERSON> chối", "approve": "Đồng ý", "request_code": "<PERSON><PERSON> đơn", "pending_approval": "<PERSON>ờ <PERSON>", "attendance": "<PERSON><PERSON><PERSON> công", "attendance_type": "<PERSON><PERSON><PERSON> chấm công", "rejected": "Đ<PERSON> từ chối", "leave_request_details": "<PERSON> tiết yêu cầu xin nghỉ", "overtime_request_details": "<PERSON> tiết yêu cầu tăng ca", "overtime": "Tăng ca", "salary_advance_request_details": "<PERSON> tiết yêu cầu tạm <PERSON>ng l<PERSON>ng", "business_trip_request_details": "<PERSON> tiết yêu cầu đi công tác", "information_update_request_details": "<PERSON> tiết yêu cầu đổi thông tin", "update_information": "<PERSON><PERSON><PERSON> nhật thông tin", "create_request": "<PERSON><PERSON><PERSON> y<PERSON> c<PERSON>u", "with_permission": "<PERSON><PERSON>", "no_permission": "Không phép", "advance_salary": "<PERSON><PERSON><PERSON>", "advance_salary_count": "<PERSON><PERSON><PERSON><PERSON> tạ<PERSON>", "unit": "Đơn vị", "content_to_be_updated": "<PERSON><PERSON><PERSON> dung cần thay đổi", "edit_request": "Chỉnh sửa yêu cầu", "early_leave": "<PERSON><PERSON>", "late_return": "<PERSON><PERSON> trễ", "leave_early": "<PERSON><PERSON> sớm", "late": "<PERSON><PERSON>", "late_and_leave_early": "<PERSON><PERSON> muộn và về sớm", "import_data_from_excel": "<PERSON><PERSON><PERSON><PERSON> dữ liệu từ excel", "upload_excel": "<PERSON><PERSON><PERSON> lên file excel", "spreadsheet_name": "<PERSON><PERSON><PERSON> trang t<PERSON>h", "download_file_template": "Tải file mẫu", "existing_data": "<PERSON><PERSON> liệu đã tồn tại", "the_data_already_exists_or_the_field_is_valid": "<PERSON><PERSON> liệu đã tồn tại hoặc trường không hợp lệ", "imported_successfully": "Đã import thà<PERSON> công", "field_is_duplicated": "<PERSON><PERSON> liệu bị trùng", "reason_rejected": "<PERSON>ý do từ chối", "request_details": "<PERSON> tiết yêu cầu", "personal_management": "<PERSON><PERSON><PERSON><PERSON> lý cá nhân", "cancel_request": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u", "reason_for_hiding_attendance_history": "<PERSON><PERSON> <PERSON> <PERSON><PERSON> lịch sử chấm công", "hidden_history_explanation": "<PERSON>ịch sử đã ẩn sẽ không được tính công và có thể khôi phục tại “Lịch sử chấm công đã ẩn”", "attendance_history_hidden": "<PERSON><PERSON> <PERSON><PERSON> lịch sử chấm công", "salary_table": "<PERSON><PERSON><PERSON>", "salary_table_name": "<PERSON><PERSON><PERSON> b<PERSON>", "total_income": "<PERSON><PERSON><PERSON> thu nhập", "total_deductions": "<PERSON><PERSON><PERSON> khấu trừ", "net_salary": "<PERSON><PERSON><PERSON><PERSON> thực n<PERSON>n", "income": "<PERSON><PERSON> <PERSON><PERSON>", "deductions": "<PERSON><PERSON><PERSON><PERSON> trừ", "additions": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "subtractions": "<PERSON><PERSON><PERSON><PERSON> trừ", "total_net_salary": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> thực n<PERSON>n", "pending": "<PERSON><PERSON> chờ", "approved": "Đ<PERSON>", "advance_list": "<PERSON><PERSON> s<PERSON>ch tạ<PERSON>", "initial": "Khởi tạo", "create_salary_table": "<PERSON><PERSON><PERSON> b<PERSON>", "application_month": "<PERSON><PERSON><PERSON><PERSON>", "applicable_employee": "<PERSON><PERSON><PERSON> viên <PERSON> dụng", "reason_for_deleting_attendance_history": "Lý do xóa lịch sử chấm công", "deleted_history_explanation": "Lịch sử đã xoá sẽ không được tính công và có thể khôi phục tại “Lịch sử chấm công đã xoá”", "leave_information": "Thông tin nghỉ phép", "annual_leave_days": "Ngày nghỉ phép năm", "sick_leave_days": "Ngày nghỉ ốm", "maternity_leave_days": "<PERSON><PERSON><PERSON> nghỉ thai sản", "wedding_leave_days": "<PERSON><PERSON>y nghỉ kết hôn", "bereavement_leave_days": "Ngày nghỉ tang gia", "annual_leave": "Nghỉ phép năm", "sick_leave": "Nghỉ ốm", "maternity_leave": "Nghỉ thai sản", "marriage_leave": "Nghỉ kết hôn", "bereavement_leave": "Nghỉ tang gia", "leave_without_permission": "Nghỉ không phép", "leave_without_permission_days": "Ngày nghỉ không phép", "no_work_shift_selected": "<PERSON><PERSON><PERSON> chọn ca làm việc", "select_work_shift": "<PERSON>ui lòng chọn ca làm việc", "additional_amount": "<PERSON><PERSON><PERSON>", "enter_additional_amounts_if_any": "<PERSON><PERSON><PERSON><PERSON> thông tin các kho<PERSON>n phát sinh nếu có", "enter_deductions_if_any": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c <PERSON> trừ phát sinh nếu có", "error": "Lỗi", "minutes": "<PERSON><PERSON><PERSON><PERSON>", "created": "Đã tạo", "sent": "Đ<PERSON> gửi", "created_and_sent": "Đã tạo và gửi", "send_salary_table": "<PERSON><PERSON><PERSON> b<PERSON>", "saved": "<PERSON><PERSON> l<PERSON>", "deleted_timekeeping_history": "<PERSON><PERSON><PERSON> sử chấm công đã xóa", "reason_for_deletion": "Lý do xóa", "timekeeping_history_restored": "<PERSON><PERSON> khôi ph<PERSON><PERSON> lịch sử chấm công", "view_all": "<PERSON><PERSON> t<PERSON>t cả", "select_branch": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "employee_already_exists_or_email_not_valid": "Nhân viên đã tồn tại hoặc email không đúng định dạng", "have_request": "<PERSON><PERSON> y<PERSON>u c<PERSON>u", "no_request": "<PERSON><PERSON><PERSON><PERSON> yêu c<PERSON>u", "approval_date": "<PERSON><PERSON><PERSON>", "email": "Email", "document": "<PERSON><PERSON><PERSON> l<PERSON>", "personal": "Cá nhân", "shared": "<PERSON><PERSON><PERSON><PERSON> chia sẻ", "personal_document": "<PERSON><PERSON><PERSON> li<PERSON>u cá nhân", "folder": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON>", "sharing": "<PERSON><PERSON> chia sẻ", "not_shared": "Không chia sẻ", "file": "<PERSON><PERSON><PERSON>", "file_name": "<PERSON><PERSON><PERSON>", "photo": "Ảnh", "audio": "<PERSON><PERSON>", "upload_folder": "<PERSON><PERSON><PERSON> thư mục lên", "create_new_folder": "<PERSON><PERSON><PERSON> thư mục mới", "rename": "<PERSON><PERSON><PERSON> tên", "share": "<PERSON><PERSON> sẻ", "download": "<PERSON><PERSON><PERSON>", "customize": "<PERSON><PERSON><PERSON> chỉnh", "show_less": "Ẩn bớt", "size": "<PERSON><PERSON><PERSON>", "shared_document": "<PERSON><PERSON><PERSON> liệu đ<PERSON> chia sẻ", "sharer": "Ngư<PERSON>i chia sẻ", "shared_by": "Chia sẻ bởi", "contract": "<PERSON><PERSON><PERSON>", "actual_received": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>n", "timekeeping_authentication": "<PERSON><PERSON><PERSON> thực chấm công", "timkeeping_confirmation": "<PERSON><PERSON><PERSON> n<PERSON>n chấm công", "upload_authentication_photo": "<PERSON><PERSON><PERSON> x<PERSON>c thực", "authentic_photo": "Ảnh xác thực", "company_statistics": "<PERSON><PERSON><PERSON><PERSON> kê công ty", "total_employees": "Tổng nhân viên", "employees_on_shift": "Nhân viên vào ca", "late_employees": "<PERSON><PERSON><PERSON> viên đi muộn", "employees_not_on_shift": "Nhân viên chưa vào ca", "statistics_and_reports": "<PERSON>h<PERSON>ng kê và báo cáo", "staff_fluctuation": "<PERSON><PERSON><PERSON><PERSON> động nhân sự", "staff_structure": "<PERSON><PERSON> cấu nhân sự", "whole_year": "<PERSON><PERSON> n<PERSON>", "contract_classification": "<PERSON><PERSON> lo<PERSON>i hợp đồng", "business": "<PERSON><PERSON>", "technical": "<PERSON><PERSON>", "design": "<PERSON><PERSON><PERSON><PERSON> kế", "accounting": "<PERSON><PERSON> toán", "fixed_term": "<PERSON><PERSON> thời hạn", "indefinite_term": "<PERSON><PERSON><PERSON><PERSON> thời hạn", "seasonal": "Thời vụ", "birthday": "<PERSON><PERSON>", "january": "Tháng 1", "february": "Tháng 2", "march": "Tháng 3", "april": "Tháng 4", "may": "Tháng 5", "june": "Tháng 6", "july": "Tháng 7", "august": "Tháng 8", "september": "Tháng 9", "october": "Tháng 10", "november": "Tháng 11", "december": "Tháng 12", "leave_holiday": "Nghỉ phép (phép và lễ)", "total_salary": "<PERSON><PERSON><PERSON> chi lư<PERSON>", "total_allowance": "Tổng phụ cấp", "total_bonus": "Tổng thưởng", "salary_chart": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> chi l<PERSON>", "amount_in_millions": "<PERSON><PERSON> tiền (triệu đồng)", "salary_structure": "<PERSON><PERSON> cấu chi l<PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "mark_all_read": "<PERSON><PERSON><PERSON> dấu tất cả đã đọc", "no_notification": "<PERSON><PERSON><PERSON>ng có thông báo", "stop_sharing": "<PERSON><PERSON>ng chia sẻ", "folder_name": "<PERSON><PERSON><PERSON> th<PERSON> mục", "no_document_shared": "<PERSON><PERSON><PERSON><PERSON> có tài liệu nào được chia sẻ", "contract_management": "<PERSON><PERSON><PERSON><PERSON> lý hợp đồng", "contract_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> đ<PERSON>ng", "customer": "<PERSON><PERSON><PERSON><PERSON>", "contract_value": "<PERSON><PERSON><PERSON> trị hợp đồng", "payment": "<PERSON><PERSON> toán", "upcoming_due": "<PERSON><PERSON><PERSON> đ<PERSON>n hạn", "still_valid": "<PERSON><PERSON><PERSON> hi<PERSON> l<PERSON>c", "initiated": "Đã khởi tạo", "expired": "<PERSON><PERSON><PERSON>", "effective_from": "<PERSON><PERSON> hi<PERSON> l<PERSON> từ", "effective_to": "<PERSON><PERSON> hi<PERSON> l<PERSON> đ<PERSON>n", "create_contract": "<PERSON><PERSON><PERSON> đ<PERSON>", "signed_date": "<PERSON><PERSON><PERSON>", "renewal_reminder": "Nhắc nhở gia hạn (ngày)", "edit_contract": "Chỉnh sửa hợp đồng", "contract_info": "<PERSON><PERSON><PERSON><PERSON> tin hợp đồng", "contract_code": "<PERSON><PERSON> hợp đồng", "paid": "<PERSON><PERSON> thanh toán", "unpaid": "<PERSON><PERSON><PERSON> to<PERSON>", "renewal_history": "<PERSON><PERSON><PERSON> sử gia hạn", "renewal_times": "<PERSON><PERSON> h<PERSON>n lần", "leave_management": "<PERSON><PERSON><PERSON><PERSON> lý nghỉ phép", "edit_number_dayoff": "Chỉnh sửa số ngày phép", "reset_leave_confirmation": "Bạn có chắc chắn muốn đặt lại ngày phép không?", "reset_leave_warning": "Số ngày phép sẽ trở về 0 sau khi đặt lại.", "update-employee": "<b>{name}</b> đ<PERSON> cập nhật thông tin", "reset-leave": "<b>{name}</b> đã đặt lại ngày nghỉ", "permanent_province_city": "Tỉnh/Th<PERSON>nh phố (theo địa chỉ thường trú)", "permanent_district": "Quận/Huyện (theo địa chỉ thường trú)", "permanent_ward": "Phường/Xã (theo địa chỉ thường trú)", "permanent_address_detail": "Địa chỉ (theo địa chỉ thường trú)", "current_province_city": "Tỉnh/<PERSON>h<PERSON>nh phố (theo chỗ ở hiện nay)", "current_district": "Quận/Huyện (theo chỗ ở hiện nay)", "current_ward": "Phường/Xã (theo chỗ ở hiện nay)", "current_address_detail": "<PERSON><PERSON><PERSON> chỉ (theo chỗ ở hiện nay)", "organizational_chart": "S<PERSON> đồ tổ chức", "level": "<PERSON><PERSON><PERSON>", "hierarchical_ranking_with_level_1_being_the_highest": "<PERSON><PERSON><PERSON> hạng thứ bậc, v<PERSON><PERSON> cấp 1 là cao nhất", "extend": "<PERSON><PERSON>", "contract_extension": "<PERSON><PERSON> hạn hợp đồng", "all_files": "<PERSON><PERSON><PERSON> cả tệp", "sort": "<PERSON><PERSON><PERSON>p", "latest": "<PERSON><PERSON><PERSON>", "oldest": "<PERSON><PERSON> n<PERSON>", "name_from_a_to_z": "<PERSON><PERSON><PERSON> từ A - <PERSON>", "name_from_z_to_a": "<PERSON><PERSON><PERSON> từ Z - A", "upload_date": "<PERSON><PERSON><PERSON> l<PERSON>n", "upload_successful": "<PERSON><PERSON> tải lên thành công", "upload_failed": "<PERSON><PERSON><PERSON> lên không thành công", "path": "Đường dẫn", "last_modified": "<PERSON><PERSON><PERSON> đ<PERSON>i lần cuối", "all_documents": "<PERSON><PERSON><PERSON> cả tài liệu", "notice_period_due": "<PERSON><PERSON><PERSON><PERSON> gian thông báo đến hạn", "password_changed_successfully": "<PERSON><PERSON> thay đổi mật khẩu thành công", "login_history": "<PERSON><PERSON><PERSON> sử đăng nh<PERSON>p", "account_login_activity": "<PERSON><PERSON><PERSON> động đăng nhập tà<PERSON>n", "this_device": "<PERSON><PERSON><PERSON><PERSON> bị <PERSON>y", "logout_successfully": "<PERSON><PERSON><PERSON> xuất thành công", "delete_shift_multiple": "<PERSON><PERSON> xóa {count} ca làm việc", "confirm_delete_multiple_shift": "Bạn có chắc chắn muốn xóa {count} ca làm việc?", "sum_salary": "<PERSON><PERSON><PERSON> l<PERSON>", "role_admin": "Quản trị viên có quyền quản lý toàn bộ hệ thống", "overtime_name": "<PERSON><PERSON><PERSON> t<PERSON> ca", "overtime_type": "Lo<PERSON><PERSON> tăng ca", "create_overtime": "<PERSON><PERSON><PERSON> t<PERSON>ng ca", "normal_day_overtime": "<PERSON><PERSON><PERSON> ca ngày thư<PERSON>ng", "weekend_overtime": "<PERSON><PERSON>ng ca cuối tuần", "holiday_overtime": "<PERSON><PERSON><PERSON> ca ngày lễ", "edit_overtime": "Chỉnh sửa tăng ca", "account_name": "<PERSON><PERSON><PERSON> đ<PERSON>p", "representative_name": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON><PERSON> đ<PERSON>n", "company_name": "<PERSON><PERSON>n công ty", "website": "Website công ty", "established_date": "<PERSON><PERSON><PERSON> th<PERSON> lập", "company_information": "Thông tin công ty", "login_information": "Th<PERSON>ng tin đăng nhập", "edit_company_information": "Chỉnh sửa thông tin công ty", "forgot_password_description": "<PERSON>h<PERSON><PERSON> địa chỉ email mà bạn sử dụng để đăng nhập, chúng tôi sẽ gửi cho bạn mã OTP để thiết lập lại mật khẩu", "all_roles": "<PERSON><PERSON><PERSON> cả các quyền", "view": "Xem", "permission_name": "<PERSON><PERSON><PERSON> quyền hạn", "sales_contract": "<PERSON><PERSON><PERSON> đồng mua bán", "service_contract": "<PERSON><PERSON><PERSON> đ<PERSON> d<PERSON> vụ", "business_cooperation_contract": "<PERSON><PERSON><PERSON> đồng hợp tác <PERSON>h do<PERSON>h", "processing_contract": "<PERSON><PERSON><PERSON> đồng gia công", "distribution_contract": "<PERSON><PERSON><PERSON> đồng phân phối", "outsourcing_contract": "<PERSON><PERSON><PERSON> đồng thuê ngo<PERSON>i", "agency_contract": "<PERSON><PERSON><PERSON> đồng đại lý", "franchise_contract": "<PERSON><PERSON><PERSON> đồ<PERSON> quyền", "contract_type_name": "<PERSON><PERSON><PERSON> lo<PERSON><PERSON> hợp đồng", "create_contract_type": "<PERSON><PERSON><PERSON> lo<PERSON> hợp đồng", "edit_contract_type": "Chỉnh sửa lo<PERSON>i hợp đồng", "manager": "<PERSON><PERSON><PERSON><PERSON> qu<PERSON>n lý", "date_reset": "Ngày đặt lại phép", "start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "message": "<PERSON>", "referral": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "invitation": "<PERSON><PERSON><PERSON> mời", "management": "<PERSON><PERSON><PERSON><PERSON> lý", "login_with_referral": "<PERSON><PERSON><PERSON> nhập với vai trò giới thiệu", "campaign": "<PERSON><PERSON><PERSON>", "referral_invitation": "<PERSON><PERSON>i mời giới thiệu", "old_password": "<PERSON><PERSON><PERSON> c<PERSON>", "company_profile": "<PERSON><PERSON> sơ công ty", "chatbot": "<PERSON><PERSON><PERSON>", "crm": "CRM", "user_referral": "<PERSON><PERSON><PERSON><PERSON> giới thiệu", "stop_bot": "<PERSON><PERSON><PERSON> chatbot", "config": "<PERSON><PERSON><PERSON> h<PERSON>nh", "created_at": "<PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "add_user": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> dùng", "labels_management": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>n", "fields_management": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> c<PERSON>t", "label": "<PERSON><PERSON>ã<PERSON>", "enter_message": "<PERSON><PERSON><PERSON><PERSON> tin nh<PERSON>n", "confirmation": "<PERSON><PERSON><PERSON>", "signup": "Đ<PERSON> đăng ký", "zalo": "<PERSON><PERSON>", "mail": "Mail"}, "button": {"detail": "<PERSON> ti<PERSON>", "create": "Tạo", "edit": "Chỉnh sửa", "delete": "Xóa", "no": "K<PERSON>ô<PERSON>", "yes": "<PERSON><PERSON>", "apply": "<PERSON><PERSON>", "back": "Trở lại", "save": "<PERSON><PERSON><PERSON>", "add_new": "<PERSON><PERSON><PERSON><PERSON> mới", "filter": "<PERSON><PERSON> lọc", "reset": "Đặt lại", "clear_all": "<PERSON><PERSON><PERSON> tất cả", "hide": "Ẩn", "cancel": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "export_excel": "Xuất excel", "import_excel": "<PERSON><PERSON><PERSON><PERSON> từ excel", "manual_addition": "<PERSON><PERSON><PERSON><PERSON> mới thủ công", "create_and_send": "Tạo và gửi", "upload": "<PERSON><PERSON><PERSON>", "export_pdf": "Xuất pdf", "only_view": "Chỉ xem", "invite": "<PERSON><PERSON><PERSON>", "clear": "Xóa"}, "notifications": {"admin": {"create-employee": "{name} đã tạo nhân viên {employeeName}", "update-employee": "{name} đ<PERSON> cập nhật nhân viên {employeeName}", "delete-employee": "{name} đã xóa nhân viên {employeeName}", "import-employee": "{name} đã import nhân viên", "create-branch": "{name} đã tạo chi nh<PERSON>h {branchName}", "update-branch": "{name} đ<PERSON> cập nhật chi nhánh {branchName}", "delete-branch": "{name} đã xóa chi nhánh {branchName}", "create-department": "{name} đã tạo phòng ban {departmentName}", "update-department": "{name} đ<PERSON> cập nhật phòng ban {departmentName}", "delete-department": "{name} đã xóa phòng ban {departmentName}", "create-shift": "{name} đã tạo ca làm việc {shiftName}", "update-shift": "{name} đ<PERSON> cập nhật ca làm việc {shiftName}", "delete-shift": "{name} đã xóa ca làm việc {shiftName}", "create-public-holiday": "{name} đ<PERSON> tạ<PERSON> ng<PERSON> l<PERSON> {holidayName}", "update-public-holiday": "{name} đ<PERSON> cập nh<PERSON>t ng<PERSON><PERSON> l<PERSON> {holidayName}", "delete-public-holiday": "{name} đ<PERSON> x<PERSON><PERSON> ng<PERSON> l<PERSON> {holidayName}", "create-allowance": "{name} đã tạo phụ cấp {allowanceName}", "update-allowance": "{name} đ<PERSON> cập nhật phụ cấp {allowanceName}", "delete-allowance": "{name} đ<PERSON> xóa phụ cấp {allowanceName}", "create-position": "{name} đã tạo chứ<PERSON> vụ {positionName}", "update-position": "{name} đ<PERSON> cập nh<PERSON>t chức vụ {positionName}", "delete-position": "{name} đ<PERSON> x<PERSON>a chức vụ {positionName}", "create-role": "{name} đã tạo vai trò {roleName}", "update-role": "{name} đ<PERSON> cập nhật vai trò {roleName}", "delete-role": "{name} đã xóa vai trò {roleName}", "create-wifi-attendance": "{name} đã tạo wifi chấm công {wifiName}", "update-wifi-attendance": "{name} đ<PERSON> cập nhật wifi chấm công {wifiName}", "delete-wifi-attendance": "{name} đã xóa wifi chấm công {wifiName}", "create-work-schedule": "{name} đ<PERSON> t<PERSON>o lịch làm việc", "update-work-schedule": "{name} đ<PERSON> cập nh<PERSON>t lịch làm vi<PERSON><PERSON> cho {employeeName}", "update-work-schedule-list": "{name} <PERSON><PERSON> cập nh<PERSON>t lịch làm vi<PERSON>c cho công ty", "delete-work-schedule": "{name} đ<PERSON> x<PERSON>a lịch làm việc", "create-salary": "{name} đ<PERSON> tạo bảng l<PERSON> {salaryTime}", "update-salary": "{name} đ<PERSON> cập nhật bảng l<PERSON> {salaryTime} của {employeeName}", "delete-salary": "{name} đ<PERSON> x<PERSON>a bảng l<PERSON> {salaryTime} của {employeeName}", "send-salary": "{name} đ<PERSON> g<PERSON>i bảng l<PERSON> {salaryTime}", "approve-salary": "{name} đ<PERSON> đồng ý bảng lương {salaryTime}", "reject-salary": "{name} đã từ chối bảng l<PERSON> {salaryTime}", "create-form": "{name} đã tạo yêu cầu {formName} cho {employeeName}", "update-form": "{name} đ<PERSON> cập nhật yêu cầu {formName} của {employeeName}", "delete-form": "{name} đã xóa yêu cầu {formName} của {employeeName}", "approve-form": "{name} đ<PERSON> du<PERSON>t yêu cầu {formName} của {employeeName}", "reject-form": "{name} đã từ chối yêu cầu {formName} của {employeeName}", "create-news": "{name} đã tạo tin tức {newsName}", "update-news": "{name} đ<PERSON> cập nhật tin tức {newsName}", "delete-news": "{name} đ<PERSON> xóa tin tức {newsName}", "update-history": "{name} <PERSON><PERSON> cập nh<PERSON>t lịch sử chấm công của {employeeName}", "delete-history": "{name} đ<PERSON> x<PERSON>a lịch sử chấm công của {employeeName}", "create-contract": "{name} đ<PERSON> tạo hợp đồng {contractName}", "update-contract": "{name} đ<PERSON> cập nhật hợp đồng {contractName}", "delete-contract": "{name} đ<PERSON> x<PERSON>a hợp đồng {contractName}", "reset-leave": "{name} đã đặt lại ngày nghỉ", "restore-history": "{name} đ<PERSON> khôi phụ<PERSON> lịch sử chấm công cho {employeeName}", "contract-expire": "{contractName} sắp hết hạn", "extend-contract": "{contractName} đ<PERSON> đ<PERSON><PERSON><PERSON> gia hạn", "create-overtime": "{name} đã tạo tăng ca {overtimeName}", "update-overtime": "{name} đ<PERSON> cập nhật tăng ca {overtimeName}", "delete-overtime": "{name} đã xóa tăng ca {overtimeName}", "create-contract-type": "{name} đã tạo lo<PERSON>i hợp đồng {contractTypeName}", "update-contract-type": "{name} đ<PERSON> cập nhật lo<PERSON>i hợp đồng {contractTypeName}", "delete-contract-type": "{name} đ<PERSON> xóa loại hợp đồng {contractTypeName}"}, "employee": {"update-employee-me": "{name} đ<PERSON> cập nhật thông tin cho bạn", "create-form-me": "{name} đã tạo yêu cầu {formName} cho bạn", "update-form-me": "{name} đ<PERSON> cập nhật yêu cầu {formName} cho bạn", "delete-form-me": "{name} đã xóa yêu cầu {formName} cho bạn", "approve-form-me": "{name} đã du<PERSON>t yêu cầu {formName} cho bạn", "reject-form-me": "{name} đã từ chối yêu cầu {formName} cho bạn", "create-news-me": "{name} đã tạo yêu cầu {newsName} cho bạn", "update-news-me": "{name} đ<PERSON> cập nhật yêu cầu {newsName} cho bạn", "delete-news-me": "{name} đã xóa yêu cầu {newsName} cho bạn", "send-salary-me": "{name} đ<PERSON> g<PERSON>i bảng l<PERSON> {salaryTime} cho bạn", "update-work-schedule-me": "{name} <PERSON><PERSON> cập nh<PERSON>t lịch làm vi<PERSON><PERSON> cho bạn", "update-history-me": "{name} <PERSON><PERSON> cập nh<PERSON>t lịch sử chấm công cho bạn", "delete-history-me": "{name} đ<PERSON> x<PERSON>a lịch sử chấm công cho bạn", "cancel-form-me": "{name} đã hủy yêu cầu {formName}", "share-file-me": "{name} đã chia sẻ tệp tin cho bạn", "share-folder-me": "{name} đã chia sẻ thư mục cho bạn", "restore-history-me": "{name} <PERSON><PERSON> khôi ph<PERSON><PERSON> lịch sử chấm công cho bạn"}}, "roles": {"employee": "<PERSON><PERSON><PERSON><PERSON> lý nhân viên", "salary": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "department": "<PERSON><PERSON><PERSON><PERSON> lý phòng ban", "allowance": "<PERSON><PERSON><PERSON><PERSON> lý phụ cấp", "wifi_attendance": "<PERSON><PERSON><PERSON><PERSON> lý wifi chấm công", "work_schedule": "<PERSON><PERSON><PERSON><PERSON> lý lịch làm vi<PERSON>", "form": "<PERSON><PERSON><PERSON><PERSON> lý yêu cầu", "shift": "<PERSON><PERSON><PERSON><PERSON> lý ca làm việc", "position": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> vụ", "history_system": "<PERSON><PERSON><PERSON><PERSON> lý chấm công", "branch": "<PERSON><PERSON><PERSON><PERSON> lý chi nh<PERSON>h", "public_holiday": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON> lễ", "role": "<PERSON><PERSON><PERSON><PERSON> lý vai trò", "news": "<PERSON><PERSON><PERSON><PERSON> lý thông báo", "contract": "<PERSON><PERSON><PERSON><PERSON> lý hợp đồng", "leave": "<PERSON><PERSON><PERSON><PERSON> lý nghỉ phép", "form_type": "<PERSON><PERSON><PERSON><PERSON> lý loại yêu cầu", "company": "Thông tin công ty", "overtime": "<PERSON><PERSON><PERSON><PERSON> lý tăng ca", "analytic": "<PERSON><PERSON><PERSON><PERSON> kê công ty", "contract_type": "<PERSON><PERSON><PERSON><PERSON> lý lo<PERSON>i hợp đồng", "view_employee": "<PERSON><PERSON> viên", "create_employee": "Tạo nhân viên", "update_employee": "<PERSON><PERSON><PERSON> nh<PERSON>t nhân viên", "delete_employee": "Xóa nhân viên", "view_salary": "<PERSON><PERSON> qu<PERSON> l<PERSON>", "create_salary": "<PERSON><PERSON><PERSON> qu<PERSON>n lý l<PERSON>", "update_salary": "<PERSON><PERSON><PERSON> nh<PERSON>t quản lý l<PERSON>", "delete_salary": "<PERSON><PERSON><PERSON> qu<PERSON>n lý l<PERSON>", "view_department": "<PERSON><PERSON> ph<PERSON> ban", "create_department": "<PERSON><PERSON><PERSON> phòng ban", "update_department": "<PERSON><PERSON><PERSON> nhật phòng ban", "delete_department": "<PERSON>óa phòng ban", "view_allowance": "<PERSON><PERSON> cấp", "create_allowance": "<PERSON><PERSON><PERSON> ph<PERSON> cấp", "update_allowance": "<PERSON><PERSON><PERSON> nh<PERSON>t phụ cấp", "delete_allowance": "<PERSON>ó<PERSON> ph<PERSON> cấp", "view_wifi_attendance": "<PERSON>em wifi chấm công", "create_wifi_attendance": "<PERSON><PERSON>o wifi chấm công", "update_wifi_attendance": "<PERSON><PERSON><PERSON> nhật wifi chấm công", "delete_wifi_attendance": "<PERSON><PERSON>a wifi chấm công", "view_work_schedule": "<PERSON><PERSON> l<PERSON> làm vi<PERSON>c", "create_work_schedule": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> làm vi<PERSON>c", "update_work_schedule": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lịch làm vi<PERSON>c", "delete_work_schedule": "<PERSON><PERSON><PERSON> l<PERSON>ch làm vi<PERSON>c", "view_form": "<PERSON><PERSON> y<PERSON>u c<PERSON>u", "create_form": "<PERSON><PERSON><PERSON> y<PERSON> c<PERSON>u", "update_form": "<PERSON><PERSON><PERSON> nh<PERSON>t yêu cầu", "delete_form": "<PERSON><PERSON><PERSON> y<PERSON> c<PERSON>u", "approve_form": "<PERSON><PERSON> yêu cầu", "view_shift": "<PERSON>em ca làm việc", "create_shift": "Tạo ca làm việc", "update_shift": "<PERSON><PERSON><PERSON> nhật ca làm việc", "delete_shift": "Xóa ca làm việc", "view_position": "<PERSON><PERSON> v<PERSON>", "create_position": "Tạ<PERSON> vụ", "update_position": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> ch<PERSON> v<PERSON>", "delete_position": "<PERSON><PERSON><PERSON> v<PERSON>", "view_history_system": "<PERSON><PERSON> qu<PERSON>n lý chấm công", "create_history_system": "<PERSON><PERSON><PERSON> qu<PERSON>n lý chấm công", "update_history_system": "<PERSON><PERSON><PERSON> nh<PERSON>t quản lý chấm công", "delete_history_system": "<PERSON><PERSON><PERSON> quản lý chấm công", "view_branch": "<PERSON>em chi nh<PERSON>h", "create_branch": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "update_branch": "<PERSON><PERSON><PERSON> nh<PERSON>t chi nh<PERSON>h", "delete_branch": "Xóa chi nh<PERSON>h", "view_public_holiday": "<PERSON><PERSON>", "create_public_holiday": "<PERSON><PERSON><PERSON>", "update_public_holiday": "<PERSON><PERSON><PERSON> nh<PERSON>t ng<PERSON> lễ", "delete_public_holiday": "<PERSON><PERSON><PERSON> ng<PERSON> l<PERSON>", "view_role": "<PERSON>em vai trò", "create_role": "Tạo vai trò", "update_role": "<PERSON><PERSON><PERSON> nhật vai trò", "delete_role": "Xóa vai trò", "view_news": "<PERSON><PERSON> thông b<PERSON>o chung", "create_news": "<PERSON><PERSON><PERSON> thông b<PERSON>o chung", "update_news": "<PERSON><PERSON><PERSON> nh<PERSON>t thông b<PERSON>o chung", "delete_news": "<PERSON><PERSON><PERSON> thông b<PERSON>o chung", "view_day_off": "<PERSON>em lo<PERSON> nghỉ phép", "view_form_type": "<PERSON><PERSON> lo<PERSON> yêu c<PERSON>u", "view_analytic": "<PERSON><PERSON> thống kê công ty", "create_contract": "<PERSON><PERSON><PERSON> đ<PERSON>", "view_contract": "<PERSON><PERSON> đ<PERSON>", "update_contract": "Chỉnh sửa hợp đồng", "delete_contract": "<PERSON><PERSON><PERSON> đ<PERSON>ng", "view_analytic_salary": "<PERSON><PERSON> th<PERSON>ng kê l<PERSON>", "view_leave": "<PERSON>em ngày nghỉ phép", "update_leave": "<PERSON><PERSON><PERSON> nhật ngày nghỉ phép"}, "error": {"BAD_REQUEST": "Lỗi yêu c<PERSON>u không hợ<PERSON> l<PERSON>, vui lòng kiểm tra", "EMAIL_EXIST": "<PERSON><PERSON> đã tồn tại", "DUPLICATE_WORK_SCHEDULE": "<PERSON><PERSON><PERSON> làm việc bị trùng lặp", "NUMBER_OF_LEAVES_INVALID": "Số ngày nghỉ vượt quá số ngày phép hiện có", "WORK_DATE_HAS_PASSED": "<PERSON>a làm việc đang ở quá khứ", "START_WORK_TIME_TAKEN": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON>t đầu làm việc đã diễn ra", "TIME_IN_SHIFT": "<PERSON>h<PERSON><PERSON> gian đang trong ca làm việc", "OLD_PASSWORD_NOT_MATCH": "<PERSON><PERSON><PERSON> kh<PERSON>u hiện tại không đúng", "FOLDER_NOT_EXIST": "<PERSON><PERSON><PERSON> li<PERSON>u không tồn tại", "PERMISSION_ACCESS_DENIED": "<PERSON><PERSON>n không có quyền truy cập tài li<PERSON>u này", "IS_AUTHOR_FOLDER": "Bạn là chủ sở hữu tài liệu này", "SHIFT_HAS_PASSED": "<PERSON><PERSON><PERSON><PERSON> thể sửa đổi ca làm việc vì ca đang bắt đầu", "ROLE_EXIST_IN_EMPLOYEE": "Tồn tại nhân viên đang đư<PERSON><PERSON> phân bổ vai trò này"}, "home": {"MỘT NỀN TẢNG - ĐA KÊNH - TỐI ĐA HIỆU QUẢ TUYỂN SINH": "MỘT NỀN TẢNG - ĐA KÊNH - TỐI ĐA HIỆU QUẢ TUYỂN SINH", "Tối Ưu Tuyển Sinh - Vận Hành Đa Kênh Hiệu Quả": "T<PERSON><PERSON> Ưu Tuyển <PERSON> - <PERSON><PERSON>n <PERSON>nh <PERSON>a <PERSON>", "Chọn giải pháp phù hợp với mọi quy mô trung tâm và trường học": "Chọn giải pháp phù hợp với mọi quy mô trung tâm và trường học – từ khởi đầu vững chắc đến quản lý tuyển sinh toàn diện. Maple STC giúp bạn tăng tốc quy trình, tư vấn 24/7, qu<PERSON><PERSON> lý hồ sơ và kết nối học sinh với trường học một cách thông minh và tự động.", "Bắt đầu ngay": "<PERSON><PERSON><PERSON> đ<PERSON>u ngay", "Chatbot thông minh - Tư vấn 24/7": "<PERSON><PERSON><PERSON> thông minh - <PERSON><PERSON> vấn 24/7", "Tăng tốc tuyển sinh với chatbot AI hỗ trợ tư vấn mọi lúc": "Tăng tốc tuyển sinh với chatbot AI hỗ trợ tư vấn mọ<PERSON> lúc, mọ<PERSON> nơ<PERSON>. Tự động tiếp nhận và xử lý thông tin từ học sinh, đồng bộ dữ liệu tứ<PERSON> thì, và giúp đội ngũ của bạn tập trung vào những cuộc tư vấn chuyên sâu hiệu quả hơn.", "Chatbot hỗ trợ tư vấn tuyển sinh 24/7": "Chatbot hỗ trợ tư vấn tuyển sinh 24/7", "Tích hợp trực tiếp với website, fanpage và các kênh tuyển sinh số": "<PERSON><PERSON><PERSON> hợp trực tiếp với website, fanpage và các kênh tuyển sinh số", "Có khả năng học hành vi người dùng và điều chỉnh câu trả lời dựa trên lịch sử tương tác": "<PERSON><PERSON>h<PERSON> năng học hành vi người dùng và điều chỉnh câu trả lời dựa trên lịch sử tương tác", "Nhân viên có thể theo dõi lịch sử trò chuyện và phối hợp hỗ trợ chuyên sâu": "<PERSON><PERSON>ân viên có thể theo dõi lịch sử trò chuyện và phối hợp hỗ trợ chuyên sâu.", "Tự động thu thập dữ liệu từ học sinh trực tiếp qua tin nhắn - nhanh chóng, không cần điền biểu mẫu": "Tự động thu thập dữ liệu từ học sinh trực tiếp qua tin nhắn - <PERSON><PERSON>h chóng, kh<PERSON>ng cần điền biểu mẫu", "CRM - Quản lý tuyển sinh chuyên nghiệp": "CRM - <PERSON><PERSON><PERSON><PERSON> lý tuyển sinh chuyên nghiệp", "Tối ưu quy trình chăm sóc học sinh với hệ thống CRM mạnh mẽ: quản lý lead hiệu quả": "Tối ưu quy trình chăm sóc học sinh với hệ thống CRM mạnh mẽ: qu<PERSON><PERSON> lý lead hi<PERSON><PERSON> qu<PERSON>, lư<PERSON> trữ tương tác tư vấn, ph<PERSON> quyền linh hoạt và cập nhật dữ liệu liên tục từ chatbot. Tất cả trong một nền tảng – trự<PERSON> quan, đồng bộ và dễ triển khai.", "Tùy biến luồng tư vấn sale": "<PERSON><PERSON><PERSON> biến luồng tư vấn sale", "Thiết lập quy trình chăm sóc học sinh theo từng chiến dịch": "<PERSON><PERSON><PERSON><PERSON> lập quy trình chăm sóc học sinh theo từng chiến dịch, từng giai đoạn. Tự do chọn cách phân loại, gắn nhãn và theo dõi hiệu quả tư vấn.", "Phân quyền không giới hạn nhân sự": "<PERSON><PERSON> quyền không giới hạn nhân sự", "Thêm bao nhiêu người dùng tùy ý": "<PERSON>h<PERSON><PERSON> bao nhiêu người dùng tùy ý. <PERSON><PERSON> quyền linh hoạt theo vai trò để phối hợp làm việc mượt mà giữa các bộ phận.", "Ghi chú và lưu trữ nội dung tư vấn": "<PERSON><PERSON> chú và lưu trữ nội dung tư vấn", "Lưu lại mọi trao đổi với học sinh theo từng hồ sơ": "<PERSON><PERSON><PERSON> lại mọi trao đổi với học sinh theo từng hồ sơ – đảm bảo không bỏ sót thông tin và dễ dàng theo dõi lịch sử chăm sóc.", "Tự động đồng bộ thông tin từ chatbot": "Tự động đồng bộ thông tin từ chatbot", "Toàn bộ dữ liệu thu thập từ chatbot được đồng bộ về CRM ngay tức thì": "Toàn bộ dữ liệu thu thập từ chatbot đư<PERSON><PERSON> đồng bộ về CRM ngay tức thì – không cần nhập tay, không lo trùng lặp.", "Cho phép cập nhật thông tin cần thu thập liên tục theo thời gian thực": "<PERSON> phép cập nhật thông tin cần thu thập liên tục theo thời gian thực", "Thông tin từ học sinh được ghi nhận và hiển thị tức thì": "Thông tin từ học sinh được ghi nhận và hiển thị tức thì, gi<PERSON><PERSON> đội ngũ tư vấn luôn làm việc với dữ liệu mới nhất – không bỏ lỡ cơ hội.", "SmartApply – Tối ưu hồ sơ & quy trình nhập học": "SmartApply – <PERSON><PERSON><PERSON> hồ sơ & quy trình nh<PERSON>p học ", "Quản lý hồ sơ và quy trình nhập học chưa bao giờ dễ dàng đến thế": "<PERSON><PERSON><PERSON><PERSON> lý hồ sơ và quy trình nhập học chưa bao giờ dễ dàng đến thế. <PERSON><PERSON><PERSON>, học sinh và nhà trường có thể theo dõi tiến độ theo thời gian thực, kết nối chặt chẽ giữa các bên liên quan và cá nhân hóa quy trình theo nhu cầu riêng.", "Theo dõi hồ sơ online thời gian thực": "<PERSON> hồ sơ online thời gian thực", "Tạo, cập nhật và theo dõi tiến độ hồ sơ mọi lúc, mọi nơi": "<PERSON><PERSON><PERSON>, cậ<PERSON> nh<PERSON>t và theo dõi tiến độ hồ sơ mọi lúc, mọ<PERSON> nơi", "Kết nối hiệu quả với đối tác tuyển sinh": "<PERSON><PERSON>t nối hiệu quả với đối tác tuyển sinh", "Liên kết chặt chẽ giữa học sinh, trường học và tư vấn viên": "<PERSON><PERSON><PERSON> kết chặt chẽ g<PERSON><PERSON><PERSON> họ<PERSON>, trư<PERSON><PERSON> học và tư vấn viên.", "Tích hợp API với phần mềm tuyển sinh của trường": "<PERSON><PERSON><PERSON> hợp API với phần mềm tuyển sinh của trường", "Đồng bộ dữ liệu nhanh chóng, giảm thao tác thủ công": "<PERSON><PERSON><PERSON> bộ dữ li<PERSON><PERSON>, g<PERSON><PERSON><PERSON> thao tác thủ công.", "Tùy biến quy trình nhập học": "<PERSON><PERSON><PERSON> biến quy trình nh<PERSON><PERSON> học", "Thiết kế lộ trình theo mô hình riêng của từng trường": "<PERSON><PERSON><PERSON><PERSON> kế lộ trình theo mô hình riêng của từng trường.", "Dữ liệu bảo mật cao, truy cập dễ dàng trên mọi thiết bị": "<PERSON><PERSON> liệu b<PERSON><PERSON> mật cao, t<PERSON><PERSON> cập dễ dàng trên mọi thiết bị.", "Dữ liệu lưu trữ an toàn trên Cloud S3": "<PERSON><PERSON> liệu lưu trữ an toàn trên Cloud S3 - <PERSON><PERSON><PERSON> xu<PERSON>t thông tin linh hoạt", "Real-time data sync with Webhooks": "<PERSON><PERSON>ng bộ dữ liệu thời gian thực với Webhooks", "Kết nối Webhook để nhận thông báo tức thì từ các nền tảng bên ngoài": "<PERSON>ết n<PERSON>i Webhook để nhận thông báo tức thì từ các nền tảng bên ngoài. Không còn phải kiểm tra thủ công.", "Company": "<PERSON><PERSON>ng ty"}}